package transcription

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

// 🎤 TranscriptionHandler obsługuje endpointy transkrypcji
type TranscriptionHandler struct {
	sttClient *NvidiaSTTClient
	logger    *zap.Logger
}

// NewTranscriptionHandler tworzy nowy handler
func NewTranscriptionHandler(orchestratorURL string, logger *zap.Logger) *TranscriptionHandler {
	return &TranscriptionHandler{
		sttClient: NewNvidiaSTTClient(orchestratorURL, logger),
		logger:    logger,
	}
}

// 🛣️ RegisterRoutes rejestruje endpointy transkrypcji
func (h *TranscriptionHandler) RegisterRoutes(router *mux.Router) {
	// Główne endpointy transkrypcji
	router.HandleFunc("/api/transcription/upload", h.UploadAndTranscribe).Methods("POST")
	router.HandleFunc("/api/transcription/status/{jobId}", h.GetTranscriptionStatus).Methods("GET")
	router.HandleFunc("/api/transcription/stats", h.GetTranscriptionStats).Methods("GET")
	router.HandleFunc("/api/transcription/health", h.GetTranscriptionHealth).Methods("GET")
	router.HandleFunc("/api/transcription/trigger-email-check", h.TriggerEmailCheck).Methods("POST")
	
	// Dashboard transkrypcji
	router.HandleFunc("/api/transcription/dashboard", h.GetTranscriptionDashboard).Methods("GET")
	
	h.logger.Info("✅ Endpointy transkrypcji zarejestrowane")
}

// 🎤 UploadAndTranscribe obsługuje upload i transkrypcję pliku
func (h *TranscriptionHandler) UploadAndTranscribe(w http.ResponseWriter, r *http.Request) {
	h.logger.Info("🎤 Otrzymano request transkrypcji")

	// Parsowanie multipart form
	if err := r.ParseMultipartForm(50 << 20); err != nil { // 50MB limit
		h.sendError(w, "Błąd parsowania formularza", http.StatusBadRequest)
		return
	}

	// Pobranie pliku
	file, header, err := r.FormFile("audio_file")
	if err != nil {
		h.sendError(w, "Brak pliku audio", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Pobranie metadanych
	emailSource := r.FormValue("email_source")
	if emailSource == "" {
		emailSource = "manual_upload"
	}

	// Odczytanie danych pliku
	audioData := make([]byte, header.Size)
	if _, err := file.Read(audioData); err != nil {
		h.sendError(w, "Błąd odczytu pliku", http.StatusInternalServerError)
		return
	}

	// Rozpoczęcie transkrypcji
	ctx, cancel := context.WithTimeout(r.Context(), 15*time.Minute)
	defer cancel()

	result, err := h.sttClient.TranscribeAudioData(ctx, audioData, header.Filename, emailSource)
	if err != nil {
		h.logger.Error("❌ Błąd transkrypcji", zap.Error(err))
		h.sendError(w, fmt.Sprintf("Błąd transkrypcji: %v", err), http.StatusInternalServerError)
		return
	}

	// Zwrócenie wyniku
	h.sendJSON(w, map[string]interface{}{
		"success": true,
		"result":  result,
		"message": "Transkrypcja zakończona pomyślnie",
	})

	h.logger.Info("✅ Transkrypcja zakończona",
		zap.String("job_id", result.JobID),
		zap.String("filename", header.Filename),
		zap.Float64("confidence", result.Confidence),
	)
}

// 📊 GetTranscriptionStatus zwraca status zadania transkrypcji
func (h *TranscriptionHandler) GetTranscriptionStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	jobID := vars["jobId"]

	if jobID == "" {
		h.sendError(w, "Brak ID zadania", http.StatusBadRequest)
		return
	}

	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	result, err := h.sttClient.GetJobStatus(ctx, jobID)
	if err != nil {
		h.logger.Error("❌ Błąd pobierania statusu", zap.Error(err))
		h.sendError(w, fmt.Sprintf("Błąd pobierania statusu: %v", err), http.StatusInternalServerError)
		return
	}

	h.sendJSON(w, map[string]interface{}{
		"success": true,
		"result":  result,
	})
}

// 📊 GetTranscriptionStats zwraca statystyki transkrypcji
func (h *TranscriptionHandler) GetTranscriptionStats(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	stats, err := h.sttClient.GetPipelineStats(ctx)
	if err != nil {
		h.logger.Error("❌ Błąd pobierania statystyk", zap.Error(err))
		h.sendError(w, fmt.Sprintf("Błąd pobierania statystyk: %v", err), http.StatusInternalServerError)
		return
	}

	h.sendJSON(w, map[string]interface{}{
		"success": true,
		"stats":   stats,
	})
}

// 🏥 GetTranscriptionHealth zwraca status systemu transkrypcji
func (h *TranscriptionHandler) GetTranscriptionHealth(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	health, err := h.sttClient.HealthCheck(ctx)
	if err != nil {
		h.logger.Error("❌ Błąd health check", zap.Error(err))
		h.sendError(w, fmt.Sprintf("Błąd health check: %v", err), http.StatusInternalServerError)
		return
	}

	h.sendJSON(w, map[string]interface{}{
		"success": true,
		"health":  health,
	})
}

// 📧 TriggerEmailCheck uruchamia sprawdzenie emaili
func (h *TranscriptionHandler) TriggerEmailCheck(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 60*time.Second)
	defer cancel()

	if err := h.sttClient.TriggerEmailCheck(ctx); err != nil {
		h.logger.Error("❌ Błąd sprawdzenia emaili", zap.Error(err))
		h.sendError(w, fmt.Sprintf("Błąd sprawdzenia emaili: %v", err), http.StatusInternalServerError)
		return
	}

	h.sendJSON(w, map[string]interface{}{
		"success": true,
		"message": "Sprawdzenie emaili uruchomione",
	})
}

// 📊 GetTranscriptionDashboard zwraca dane dla dashboardu transkrypcji
func (h *TranscriptionHandler) GetTranscriptionDashboard(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	// Pobieranie metryk
	metrics, err := h.sttClient.GetTranscriptionMetrics(ctx)
	if err != nil {
		h.logger.Error("❌ Błąd pobierania metryk", zap.Error(err))
		h.sendError(w, fmt.Sprintf("Błąd pobierania metryk: %v", err), http.StatusInternalServerError)
		return
	}

	// Przygotowanie danych dashboardu
	dashboard := map[string]interface{}{
		"title":       "🎤 Dashboard Transkrypcji HVAC",
		"subtitle":    "NVIDIA STT + Gemma 3 4B Analysis",
		"timestamp":   time.Now().Unix(),
		"metrics":     metrics,
		"features": []map[string]interface{}{
			{
				"name":        "NVIDIA STT Polski",
				"description": "FastConformer Hybrid Large PC",
				"status":      "active",
				"icon":        "🎤",
			},
			{
				"name":        "Gemma 3 4B Analysis",
				"description": "AI analiza transkrypcji",
				"status":      "active",
				"icon":        "🧠",
			},
			{
				"name":        "Email Monitoring",
				"description": "Automatyczne przetwarzanie M4A",
				"status":      "active",
				"icon":        "📧",
			},
			{
				"name":        "Audio Conversion",
				"description": "M4A → WAV pipeline",
				"status":      "active",
				"icon":        "🔄",
			},
		},
		"recent_activity": h.getRecentActivity(metrics),
	}

	h.sendJSON(w, map[string]interface{}{
		"success":   true,
		"dashboard": dashboard,
	})
}

// 📈 getRecentActivity generuje dane o ostatniej aktywności
func (h *TranscriptionHandler) getRecentActivity(metrics map[string]interface{}) []map[string]interface{} {
	activity := []map[string]interface{}{}

	// Sprawdzenie statystyk pipeline'u
	if pipelineStats, ok := metrics["pipeline_stats"].(map[string]interface{}); ok {
		if totalJobs, ok := pipelineStats["total_jobs"].(float64); ok && totalJobs > 0 {
			activity = append(activity, map[string]interface{}{
				"type":        "transcription",
				"message":     fmt.Sprintf("Przetworzono %.0f zadań transkrypcji", totalJobs),
				"timestamp":   time.Now().Add(-1 * time.Hour).Unix(),
				"icon":        "🎤",
				"status":      "success",
			})
		}

		if completedJobs, ok := pipelineStats["completed_jobs"].(float64); ok && completedJobs > 0 {
			activity = append(activity, map[string]interface{}{
				"type":        "completion",
				"message":     fmt.Sprintf("Zakończono %.0f transkrypcji", completedJobs),
				"timestamp":   time.Now().Add(-30 * time.Minute).Unix(),
				"icon":        "✅",
				"status":      "success",
			})
		}
	}

	// Sprawdzenie health systemu
	if systemHealth, ok := metrics["system_health"].(map[string]interface{}); ok {
		if status, ok := systemHealth["status"].(string); ok && status == "healthy" {
			activity = append(activity, map[string]interface{}{
				"type":        "system",
				"message":     "System transkrypcji działa poprawnie",
				"timestamp":   time.Now().Add(-5 * time.Minute).Unix(),
				"icon":        "🏥",
				"status":      "success",
			})
		}
	}

	// Domyślna aktywność jeśli brak danych
	if len(activity) == 0 {
		activity = append(activity, map[string]interface{}{
			"type":        "system",
			"message":     "System transkrypcji uruchomiony",
			"timestamp":   time.Now().Unix(),
			"icon":        "🚀",
			"status":      "info",
		})
	}

	return activity
}

// 🔧 Pomocnicze metody

func (h *TranscriptionHandler) sendJSON(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.Error("❌ Błąd kodowania JSON", zap.Error(err))
		http.Error(w, "Błąd serwera", http.StatusInternalServerError)
	}
}

func (h *TranscriptionHandler) sendError(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(statusCode)

	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
		"code":    statusCode,
	}

	json.NewEncoder(w).Encode(errorResponse)
	h.logger.Error("❌ API Error", zap.String("message", message), zap.Int("code", statusCode))
}

// 📊 GetTranscriptionMetricsForDashboard zwraca metryki dla głównego dashboardu HVAC
func (h *TranscriptionHandler) GetTranscriptionMetricsForDashboard() map[string]interface{} {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	metrics, err := h.sttClient.GetTranscriptionMetrics(ctx)
	if err != nil {
		h.logger.Warn("⚠️ Nie udało się pobrać metryk transkrypcji", zap.Error(err))
		return map[string]interface{}{
			"status": "unavailable",
			"error":  err.Error(),
		}
	}

	// Przygotowanie uproszczonych metryk dla głównego dashboardu
	simplified := map[string]interface{}{
		"status":              "active",
		"total_transcriptions": 0,
		"success_rate":        0.0,
		"avg_processing_time": 0.0,
		"last_24h":           0,
	}

	if pipelineStats, ok := metrics["pipeline_stats"].(map[string]interface{}); ok {
		if total, ok := pipelineStats["total_jobs"].(float64); ok {
			simplified["total_transcriptions"] = int(total)
		}
		if completed, ok := pipelineStats["completed_jobs"].(float64); ok {
			if total, ok := pipelineStats["total_jobs"].(float64); ok && total > 0 {
				simplified["success_rate"] = (completed / total) * 100
			}
		}
		if avgTime, ok := pipelineStats["average_processing_time"].(float64); ok {
			simplified["avg_processing_time"] = avgTime
		}
		if last24h, ok := pipelineStats["last_24h_jobs"].(float64); ok {
			simplified["last_24h"] = int(last24h)
		}
	}

	return simplified
}
