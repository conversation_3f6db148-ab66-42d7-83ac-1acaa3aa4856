# HVAC-Remix

This is the main README file for the HVAC-Remix project. It provides an overview of the project, its features, and how to get started.

## Features
- Comprehensive CRM system for managing HVAC businesses.
- Modern frontend built with React and Remix.
- Robust backend built with Go and gRPC.
- AI-driven insights and automation.
- Real-time data synchronization and analytics.

## Getting Started
1. **Clone the repository:**
   ```sh
   git clone https://github.com/your-repo/HVAC-Remix.git
   cd HVAC-Remix
   ```

2. **Install dependencies:**
   ```sh
   npm install
   ```

3. **Start the development server:**
   ```sh
   npm run dev
   ```

## Contributing
Contributions are welcome! Please read the [CONTRIBUTING.md](CONTRIBUTING.md) file for details on how to contribute.

## License
This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.
