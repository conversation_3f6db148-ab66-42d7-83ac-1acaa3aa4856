# Contributing Guide

This guide provides instructions on how to contribute to the HVAC-Remix project.

## How to Contribute
1. **Fork the Repository:**
   - Go to the [HVAC-Remix repository](https://github.com/your-repo/HVAC-Remix) and click the "Fork" button.

2. **Clone Your Forked Repository:**
   ```sh
   git clone https://github.com/your-username/HVAC-Remix.git
   cd HVAC-Remix
   ```

3. **Create a New Branch:**
   ```sh
   git checkout -b your-feature-branch
   ```

4. **Make Your Changes:**
   - Implement your feature or fix the bug.
   - Ensure your code follows the project's coding standards.

5. **Test Your Changes:**
   - Run the test suite to ensure your changes do not break existing functionality.
   ```sh
   npm test
   ```

6. **Commit Your Changes:**
   ```sh
   git commit -am "Add your commit message here"
   ```

7. **Push to Your Branch:**
   ```sh
   git push origin your-feature-branch
   ```

8. **Create a Pull Request:**
   - Go to your forked repository on GitHub and click the "New pull request" button.
   - Provide a clear description of your changes and the problem they solve.

## Code of Conduct
Please follow the [Code of Conduct](CODE_OF_CONDUCT.md) when contributing to this project.

## License
This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

## Additional Resources
- [README.md](README.md)
- [CONFIGURATION.md](CONFIGURATION.md)
- [USAGE.md](USAGE.md)
