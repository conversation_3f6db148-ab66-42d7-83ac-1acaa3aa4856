# Code of Conduct

## Our Pledge

We as that everyone in our community is treated with respect and dignity. We are committed to providing a welcoming and inclusive environment for all contributors, regardless of their background, identity, or contributions.

## Our Standards

Examples of behavior that contributes to a positive environment include:

- Using welcoming and inclusive language.
- Being respectful of differing viewpoints and experiences.
- Gracefully accepting constructive criticism.
- Focusing on what is best for the community.
- Showing empathy towards other community members.

Examples of unacceptable behavior include:

- Using sexualized language or imagery.
- Making derogatory comments about a person's identity or background.
- Harassment through public or private communication.
- Publishing others' private information, such as a physical or email address, without explicit permission.
- Other conduct which could reasonably be considered inappropriate in a professional setting.

## Our Responsibilities

Project maintainers are responsible for clarifying the standards of acceptable behavior and are expected to take appropriate and when necessary.

## Scope

This Code of Conduct applies both within project spaces and in public spaces when an individual is representing the project or its community.

## Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be reported by contacting the project maintainers at [<EMAIL>](mailto:<EMAIL>).

The project maintainers will review and investigate all complaints promptly and fairly. If a determination is made that a particular action was in violation of this Code of Conduct, the relevant project maintainer(s) will take any action they deem appropriate, up to and including a temporary or permanent ban from the project.

## Attribution

This Code of Conduct is adapted from the [ Contributorutor Covenant](https://www.contributor-covenant.org/), version 2.1, available at https://www.contributor-covenant.org/version/2/1/code_of_conduct.txt.
