#!/usr/bin/env python3
"""Fix relative imports in Krabulon modules."""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path: Path):
    """Fix relative imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Skip if already has sys.path fix
        if 'sys.path.insert(0, str(Path(__file__).parent.parent))' in content:
            print(f"Already fixed: {file_path}")
            return
        
        # Pattern to match relative imports
        patterns = [
            (r'from \.\.config import', 'from config import'),
            (r'from \.\.models import', 'from models import'),
            (r'from \.\.database import', 'from database import'),
            (r'from \.\.agents import', 'from agents import'),
            (r'from \.\.crawlers import', 'from crawlers import'),
        ]
        
        # Check if file has any relative imports
        has_relative_imports = any(re.search(pattern[0], content) for pattern in patterns)
        
        if not has_relative_imports:
            print(f"No relative imports: {file_path}")
            return
        
        # Add sys.path fix at the top after other imports
        import_section = []
        other_lines = []
        in_imports = True
        
        lines = content.split('\n')
        for line in lines:
            if in_imports and (line.startswith('import ') or line.startswith('from ') or line.strip() == '' or line.startswith('#')):
                import_section.append(line)
            else:
                if in_imports:
                    # Add sys.path fix before first non-import line
                    import_section.extend([
                        '',
                        'import sys',
                        'from pathlib import Path',
                        '',
                        '# Add parent directory to path for imports',
                        'sys.path.insert(0, str(Path(__file__).parent.parent))',
                        ''
                    ])
                    in_imports = False
                other_lines.append(line)
        
        # Apply pattern replacements
        new_content = '\n'.join(import_section + other_lines)
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, new_content)
        
        # Write back
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Fixed: {file_path}")
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")

def main():
    """Fix imports in all Python files."""
    krabulon_dir = Path(__file__).parent
    
    # Find all Python files
    python_files = []
    for root, dirs, files in os.walk(krabulon_dir):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py') and file != 'fix_imports.py':
                python_files.append(Path(root) / file)
    
    print(f"Found {len(python_files)} Python files to check")
    
    for file_path in python_files:
        fix_imports_in_file(file_path)
    
    print("Import fixing complete!")

if __name__ == "__main__":
    main()
