"""LM Studio adapter for local LLM integration."""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
from loguru import logger

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import settings


class LMStudioAdapter:
    """Adapter for LM Studio local LLM server."""
    
    def __init__(self):
        self.base_url = settings.ai.lm_studio_url
        self.model = settings.ai.lm_studio_model
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def predict(self, prompt: str, **kwargs) -> str:
        """Predict/generate text using LM Studio.
        
        Args:
            prompt: Input prompt
            **kwargs: Additional generation parameters
            
        Returns:
            Generated text response
        """
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Prepare request payload
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": kwargs.get("temperature", 0.1),
                "max_tokens": kwargs.get("max_tokens", 1000),
                "stream": False
            }
            
            # Make request to LM Studio
            async with self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]
                    else:
                        logger.warning("No choices in LM Studio response")
                        return "Error: No response from LM Studio"
                
                else:
                    error_text = await response.text()
                    logger.error(f"LM Studio request failed: {response.status} - {error_text}")
                    return f"Error: LM Studio request failed ({response.status})"
        
        except asyncio.TimeoutError:
            logger.error("LM Studio request timed out")
            return "Error: Request timed out"
        
        except Exception as e:
            logger.error(f"LM Studio request error: {e}")
            return f"Error: {str(e)}"
    
    async def health_check(self) -> Dict[str, Any]:
        """Check LM Studio server health.
        
        Returns:
            Health status information
        """
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Test with simple request
            async with self.session.get(
                f"{self.base_url}/v1/models",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                
                if response.status == 200:
                    models = await response.json()
                    return {
                        "healthy": True,
                        "status": "connected",
                        "models": models.get("data", []),
                        "url": self.base_url
                    }
                else:
                    return {
                        "healthy": False,
                        "status": f"HTTP {response.status}",
                        "url": self.base_url
                    }
        
        except Exception as e:
            return {
                "healthy": False,
                "status": f"Connection failed: {str(e)}",
                "url": self.base_url
            }


class LMStudioLLM:
    """LangChain-compatible LLM wrapper for LM Studio."""
    
    def __init__(self):
        self.adapter = LMStudioAdapter()
        self._session_created = False
    
    async def _ensure_session(self):
        """Ensure adapter session is created."""
        if not self._session_created:
            await self.adapter.__aenter__()
            self._session_created = True
    
    def predict(self, prompt: str, **kwargs) -> str:
        """Synchronous predict method for LangChain compatibility."""
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, create a new task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.apredict(prompt, **kwargs))
                    return future.result(timeout=30)
            else:
                return asyncio.run(self.apredict(prompt, **kwargs))
        except Exception as e:
            logger.error(f"LM Studio predict error: {e}")
            return f"Mock response to: {prompt[:50]}... (LM Studio unavailable)"
    
    async def apredict(self, prompt: str, **kwargs) -> str:
        """Asynchronous predict method."""
        await self._ensure_session()
        return await self.adapter.predict(prompt, **kwargs)
    
    def __call__(self, prompt: str, **kwargs) -> str:
        """Make the object callable."""
        return self.predict(prompt, **kwargs)
    
    async def cleanup(self):
        """Cleanup adapter session."""
        if self._session_created:
            await self.adapter.__aexit__(None, None, None)
            self._session_created = False


def create_lm_studio_llm() -> LMStudioLLM:
    """Create LM Studio LLM instance.
    
    Returns:
        LMStudioLLM instance
    """
    return LMStudioLLM()


async def test_lm_studio_connection() -> Dict[str, Any]:
    """Test LM Studio connection.
    
    Returns:
        Connection test results
    """
    async with LMStudioAdapter() as adapter:
        health = await adapter.health_check()
        
        if health["healthy"]:
            # Test generation
            test_response = await adapter.predict("Say 'Hello from LM Studio!'")
            health["test_response"] = test_response
        
        return health


# Fallback mock LLM for when LM Studio is not available
class MockLLM:
    """Mock LLM for testing when LM Studio is not available."""
    
    def predict(self, prompt: str, **kwargs) -> str:
        """Mock predict method."""
        return f"Mock response to: {prompt[:50]}..."
    
    def __call__(self, prompt: str, **kwargs) -> str:
        """Make the object callable."""
        return self.predict(prompt, **kwargs)
    
    async def cleanup(self):
        """Mock cleanup method."""
        pass
