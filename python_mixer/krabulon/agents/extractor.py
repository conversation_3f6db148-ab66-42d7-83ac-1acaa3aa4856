
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Extractor Agent for processing and structuring crawled HVAC data."""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent
from models import HVACEquipment, BasicInfo, TechnicalSpecs, Metadata


class ExtractorAgent(BaseAgent):
    """Agent responsible for extracting and structuring HVAC data from crawled content."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="Extractor",
            role="HVAC Data Extraction Specialist",
            goal="Extract and structure comprehensive HVAC equipment data from web content",
            backstory=(
                "You are an expert in HVAC technical specifications and data extraction. "
                "You understand equipment specifications, energy ratings, technical parameters, "
                "and industry standards. Your expertise helps identify and extract the most "
                "valuable technical data from complex product documentation and catalogs."
            ),
            **kwargs
        )
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute data extraction from crawled content.
        
        Args:
            input_data: Crawled content and extraction parameters
            **kwargs: Additional extraction parameters
            
        Returns:
            Structured HVAC equipment data
        """
        try:
            logger.info("Starting HVAC data extraction")
            
            # Validate input
            if not self.validate_input(input_data, ["raw_data"]):
                raise ValueError("Invalid input data for extraction")
            
            raw_data = input_data["raw_data"]
            extraction_config = input_data.get("config", {})
            
            # Extract equipment data from raw crawled content
            extracted_equipment = await self._extract_equipment_data(
                raw_data,
                extraction_config,
                **kwargs
            )
            
            # Validate and clean extracted data
            validated_equipment = await self._validate_extracted_data(extracted_equipment)
            
            # Enrich data with additional processing
            enriched_equipment = await self._enrich_equipment_data(validated_equipment)
            
            # Compile extraction results
            extraction_results = {
                "extraction_summary": {
                    "total_items_processed": len(raw_data),
                    "equipment_extracted": len(enriched_equipment),
                    "extraction_rate": len(enriched_equipment) / max(1, len(raw_data)),
                    "timestamp": datetime.now().isoformat(),
                    "extractor_version": "1.0.0"
                },
                "extracted_equipment": enriched_equipment,
                "quality_metrics": await self._calculate_quality_metrics(enriched_equipment)
            }
            
            logger.info(f"Data extraction completed: {len(enriched_equipment)} equipment items")
            return extraction_results
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
    
    async def _extract_equipment_data(
        self,
        raw_data: List[Dict[str, Any]],
        config: Dict[str, Any],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Extract equipment data from raw crawled content."""
        
        extracted_equipment = []
        
        for item in raw_data:
            if not item.get("success") or not item.get("content"):
                continue
            
            try:
                # Extract equipment from content using AI
                equipment_items = await self._extract_from_content(
                    content=item["content"],
                    source_url=item.get("url", ""),
                    manufacturer=item.get("manufacturer", ""),
                    config=config
                )
                
                extracted_equipment.extend(equipment_items)
                
            except Exception as e:
                logger.error(f"Failed to extract from content: {e}")
        
        return extracted_equipment
    
    async def _extract_from_content(
        self,
        content: str,
        source_url: str,
        manufacturer: str,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract HVAC equipment data from content using AI."""
        
        extraction_prompt = self._create_extraction_prompt(
            content, source_url, manufacturer, config
        )
        
        try:
            response = await self.run_task(extraction_prompt)
            
            # Parse JSON response
            equipment_data = json.loads(response)
            
            # Ensure it's a list
            if isinstance(equipment_data, dict):
                equipment_data = [equipment_data]
            elif not isinstance(equipment_data, list):
                equipment_data = []
            
            # Add metadata to each item
            for item in equipment_data:
                item["source_url"] = source_url
                item["extraction_date"] = datetime.now().isoformat()
                item["extractor"] = "ExtractorAgent"
                
                # Set manufacturer if not present
                if not item.get("manufacturer") and manufacturer:
                    item["manufacturer"] = manufacturer
            
            return equipment_data
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to extract equipment data: {e}")
            
            # Fallback to pattern-based extraction
            return await self._fallback_extraction(content, source_url, manufacturer)
    
    def _create_extraction_prompt(
        self,
        content: str,
        source_url: str,
        manufacturer: str,
        config: Dict[str, Any]
    ) -> str:
        """Create AI prompt for equipment data extraction."""
        
        # Truncate content if too long
        max_content_length = config.get("max_content_length", 8000)
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."
        
        prompt = f"""
        Extract HVAC equipment technical specifications from the following content.
        
        Source: {source_url}
        Manufacturer: {manufacturer or "Unknown"}
        
        Content:
        {content}
        
        Extract all HVAC equipment with complete technical specifications in JSON format.
        Return an array of equipment objects with this exact structure:
        
        [
            {{
                "basic_info": {{
                    "manufacturer": "string",
                    "model": "string", 
                    "type": "air_conditioner|heat_pump|ventilation|chiller|boiler|furnace|split_system|multi_split|vrf",
                    "series": "string or null",
                    "release_year": number or null
                }},
                "technical_specs": {{
                    "cooling_capacity": {{"value": number, "unit": "kW|BTU/h|W"}},
                    "heating_capacity": {{"value": number, "unit": "kW|BTU/h|W"}},
                    "energy_efficiency": {{
                        "cooling_eer": number or null,
                        "heating_cop": number or null,
                        "seer": number or null,
                        "scop": number or null,
                        "energy_class": "A+++|A++|A+|A|B|C|D|E|F|G" or null
                    }},
                    "dimensions": {{
                        "width": number or null,
                        "height": number or null,
                        "depth": number or null,
                        "unit": "mm|cm|m"
                    }},
                    "weight": {{"value": number or null, "unit": "kg|lb"}},
                    "refrigerant": {{
                        "type": "R32|R410A|R134a|R407C|etc" or null,
                        "charge": number or null,
                        "unit": "kg|lb"
                    }},
                    "electrical": {{
                        "power_supply": "string" or null,
                        "cooling_power": number or null,
                        "heating_power": number or null,
                        "unit": "W|kW"
                    }},
                    "airflow": {{"value": number or null, "unit": "m³/h|CFM"}},
                    "noise_level": {{
                        "indoor": number or null,
                        "outdoor": number or null,
                        "unit": "dB(A)|dB"
                    }},
                    "operating_range": {{
                        "cooling": {{"min": number or null, "max": number or null}},
                        "heating": {{"min": number or null, "max": number or null}},
                        "unit": "°C|°F"
                    }}
                }},
                "features": ["array", "of", "feature", "strings"],
                "certifications": ["array", "of", "certification", "strings"],
                "documentation": {{
                    "manuals": ["array", "of", "manual", "URLs"],
                    "datasheets": ["array", "of", "datasheet", "URLs"],
                    "installation_guides": ["array", "of", "guide", "URLs"]
                }},
                "confidence_score": 0.0-1.0
            }}
        ]
        
        Important guidelines:
        1. Only extract actual HVAC equipment (air conditioners, heat pumps, etc.)
        2. Include numerical values with proper units
        3. Use null for missing data, don't make up values
        4. Set confidence_score based on data completeness and clarity
        5. Extract all equipment found, not just the first one
        6. Focus on technical specifications and measurable parameters
        7. Include model numbers, series, and specific identifiers
        
        Return only valid JSON array, no additional text.
        """
        
        return prompt
    
    async def _fallback_extraction(
        self,
        content: str,
        source_url: str,
        manufacturer: str
    ) -> List[Dict[str, Any]]:
        """Fallback pattern-based extraction when AI fails."""
        
        logger.info("Using fallback pattern-based extraction")
        
        equipment_items = []
        
        # Pattern matching for common HVAC data
        patterns = {
            "model": r"(?:model|type)\s*:?\s*([A-Z0-9\-]+)",
            "cooling_capacity": r"cooling\s*capacity\s*:?\s*([0-9.,]+)\s*(kw|btu|w)",
            "heating_capacity": r"heating\s*capacity\s*:?\s*([0-9.,]+)\s*(kw|btu|w)",
            "energy_class": r"energy\s*class\s*:?\s*([A-G]\+*)",
            "refrigerant": r"refrigerant\s*:?\s*(R[0-9A-Z]+)",
            "power": r"power\s*consumption\s*:?\s*([0-9.,]+)\s*(w|kw)"
        }
        
        extracted_data = {}
        
        for field, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                if isinstance(matches[0], tuple):
                    extracted_data[field] = {"value": matches[0][0], "unit": matches[0][1]}
                else:
                    extracted_data[field] = matches[0]
        
        # Create equipment item if we found some data
        if extracted_data:
            equipment_item = {
                "basic_info": {
                    "manufacturer": manufacturer or "Unknown",
                    "model": extracted_data.get("model", "Unknown"),
                    "type": "air_conditioner",  # Default assumption
                    "series": None,
                    "release_year": None
                },
                "technical_specs": {},
                "features": [],
                "certifications": [],
                "documentation": {"manuals": [], "datasheets": [], "installation_guides": []},
                "source_url": source_url,
                "extraction_date": datetime.now().isoformat(),
                "extractor": "ExtractorAgent_Fallback",
                "confidence_score": 0.3  # Low confidence for pattern matching
            }
            
            # Add technical specs if found
            if "cooling_capacity" in extracted_data:
                equipment_item["technical_specs"]["cooling_capacity"] = extracted_data["cooling_capacity"]
            
            if "heating_capacity" in extracted_data:
                equipment_item["technical_specs"]["heating_capacity"] = extracted_data["heating_capacity"]
            
            if "energy_class" in extracted_data:
                equipment_item["technical_specs"]["energy_efficiency"] = {
                    "energy_class": extracted_data["energy_class"]
                }
            
            equipment_items.append(equipment_item)
        
        return equipment_items
    
    async def _validate_extracted_data(
        self,
        equipment_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Validate and clean extracted equipment data."""
        
        validated_equipment = []
        
        for item in equipment_data:
            try:
                # Create HVACEquipment model for validation
                hvac_equipment = self._create_hvac_equipment_model(item)
                
                # Check if equipment is complete enough
                if hvac_equipment.is_complete():
                    validated_equipment.append(hvac_equipment.to_dict())
                else:
                    logger.warning(f"Incomplete equipment data: {hvac_equipment.get_missing_fields()}")
                    
                    # Include if it has minimum required data
                    if hvac_equipment.basic_info.manufacturer and hvac_equipment.basic_info.model:
                        validated_equipment.append(hvac_equipment.to_dict())
                
            except Exception as e:
                logger.error(f"Failed to validate equipment data: {e}")
        
        return validated_equipment
    
    def _create_hvac_equipment_model(self, data: Dict[str, Any]) -> HVACEquipment:
        """Create HVACEquipment model from extracted data."""
        
        # Extract basic info
        basic_info_data = data.get("basic_info", {})
        basic_info = BasicInfo(
            manufacturer=basic_info_data.get("manufacturer", "Unknown"),
            model=basic_info_data.get("model", "Unknown"),
            type=basic_info_data.get("type", "air_conditioner"),
            series=basic_info_data.get("series"),
            release_year=basic_info_data.get("release_year")
        )
        
        # Extract technical specs
        tech_specs_data = data.get("technical_specs", {})
        technical_specs = None
        if tech_specs_data:
            technical_specs = TechnicalSpecs(**tech_specs_data)
        
        # Extract metadata
        metadata = Metadata(
            source_url=data.get("source_url", ""),
            extraction_date=datetime.fromisoformat(
                data.get("extraction_date", datetime.now().isoformat())
            ),
            confidence_score=data.get("confidence_score", 0.5),
            extractor_version=data.get("extractor", "1.0.0")
        )
        
        # Create equipment model
        equipment = HVACEquipment(
            basic_info=basic_info,
            technical_specs=technical_specs,
            features=data.get("features", []),
            certifications=data.get("certifications", []),
            documentation=data.get("documentation"),
            metadata=metadata
        )
        
        return equipment
    
    async def _enrich_equipment_data(
        self,
        equipment_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Enrich equipment data with additional processing."""
        
        enriched_equipment = []
        
        for item in equipment_data:
            try:
                # Add unique identifier
                item["id"] = self._generate_equipment_id(item)
                
                # Normalize units
                item = await self._normalize_units(item)
                
                # Add calculated fields
                item = await self._add_calculated_fields(item)
                
                # Enhance confidence score
                item = await self._enhance_confidence_score(item)
                
                enriched_equipment.append(item)
                
            except Exception as e:
                logger.error(f"Failed to enrich equipment data: {e}")
                enriched_equipment.append(item)  # Include original data
        
        return enriched_equipment
    
    def _generate_equipment_id(self, equipment: Dict[str, Any]) -> str:
        """Generate unique identifier for equipment."""
        
        basic_info = equipment.get("basic_info", {})
        manufacturer = basic_info.get("manufacturer", "unknown")
        model = basic_info.get("model", "unknown")
        equipment_type = basic_info.get("type", "unknown")
        
        # Create hash-like ID
        import hashlib
        id_string = f"{manufacturer}_{model}_{equipment_type}".lower()
        return hashlib.md5(id_string.encode()).hexdigest()[:12]
    
    async def _normalize_units(self, equipment: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize units to standard formats."""
        
        # Unit conversion mappings
        unit_conversions = {
            "capacity": {
                "btu/h": {"factor": 0.000293071, "target": "kW"},
                "btu": {"factor": 0.000293071, "target": "kW"},
                "w": {"factor": 0.001, "target": "kW"}
            },
            "temperature": {
                "f": {"formula": "celsius", "target": "°C"}
            },
            "weight": {
                "lb": {"factor": 0.453592, "target": "kg"}
            }
        }
        
        tech_specs = equipment.get("technical_specs", {})
        
        # Normalize cooling capacity
        if "cooling_capacity" in tech_specs:
            tech_specs["cooling_capacity"] = self._convert_unit(
                tech_specs["cooling_capacity"], unit_conversions["capacity"]
            )
        
        # Normalize heating capacity
        if "heating_capacity" in tech_specs:
            tech_specs["heating_capacity"] = self._convert_unit(
                tech_specs["heating_capacity"], unit_conversions["capacity"]
            )
        
        return equipment
    
    def _convert_unit(self, value_unit: Dict[str, Any], conversions: Dict[str, Any]) -> Dict[str, Any]:
        """Convert unit to standard format."""
        
        if not isinstance(value_unit, dict) or "unit" not in value_unit:
            return value_unit
        
        unit = value_unit["unit"].lower()
        value = value_unit.get("value")
        
        if unit in conversions and isinstance(value, (int, float)):
            conversion = conversions[unit]
            if "factor" in conversion:
                new_value = value * conversion["factor"]
                return {"value": round(new_value, 3), "unit": conversion["target"]}
        
        return value_unit
    
    async def _add_calculated_fields(self, equipment: Dict[str, Any]) -> Dict[str, Any]:
        """Add calculated fields based on existing data."""
        
        tech_specs = equipment.get("technical_specs", {})
        
        # Calculate power efficiency if possible
        cooling_capacity = tech_specs.get("cooling_capacity", {})
        electrical = tech_specs.get("electrical", {})
        
        if (cooling_capacity.get("value") and electrical.get("cooling_power") and
            cooling_capacity.get("unit") == "kW" and electrical.get("unit") == "W"):
            
            capacity_kw = cooling_capacity["value"]
            power_kw = electrical["cooling_power"] / 1000
            
            if power_kw > 0:
                eer = capacity_kw / power_kw
                if "energy_efficiency" not in tech_specs:
                    tech_specs["energy_efficiency"] = {}
                tech_specs["energy_efficiency"]["calculated_eer"] = round(eer, 2)
        
        return equipment
    
    async def _enhance_confidence_score(self, equipment: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance confidence score based on data completeness."""
        
        # Count available fields
        total_fields = 0
        filled_fields = 0
        
        # Check basic info
        basic_info = equipment.get("basic_info", {})
        for field in ["manufacturer", "model", "type", "series"]:
            total_fields += 1
            if basic_info.get(field):
                filled_fields += 1
        
        # Check technical specs
        tech_specs = equipment.get("technical_specs", {})
        spec_fields = [
            "cooling_capacity", "heating_capacity", "energy_efficiency",
            "dimensions", "weight", "refrigerant", "electrical"
        ]
        for field in spec_fields:
            total_fields += 1
            if tech_specs.get(field):
                filled_fields += 1
        
        # Calculate completeness score
        completeness_score = filled_fields / total_fields if total_fields > 0 else 0
        
        # Combine with original confidence
        original_confidence = equipment.get("confidence_score", 0.5)
        enhanced_confidence = (original_confidence + completeness_score) / 2
        
        equipment["confidence_score"] = round(enhanced_confidence, 3)
        
        return equipment
    
    async def _calculate_quality_metrics(
        self,
        equipment_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate quality metrics for extracted data."""
        
        if not equipment_data:
            return {"average_confidence": 0, "completeness_rate": 0, "total_items": 0}
        
        total_confidence = sum(item.get("confidence_score", 0) for item in equipment_data)
        average_confidence = total_confidence / len(equipment_data)
        
        # Count complete items (having basic info + some technical specs)
        complete_items = 0
        for item in equipment_data:
            basic_info = item.get("basic_info", {})
            tech_specs = item.get("technical_specs", {})
            
            if (basic_info.get("manufacturer") and basic_info.get("model") and
                tech_specs and len(tech_specs) > 0):
                complete_items += 1
        
        completeness_rate = complete_items / len(equipment_data)
        
        return {
            "total_items": len(equipment_data),
            "complete_items": complete_items,
            "completeness_rate": round(completeness_rate, 3),
            "average_confidence": round(average_confidence, 3),
            "high_confidence_items": len([
                item for item in equipment_data 
                if item.get("confidence_score", 0) > 0.7
            ])
        }
