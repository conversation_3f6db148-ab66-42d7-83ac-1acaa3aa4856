2025-05-30 14:21:27 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Planner agent
2025-05-30 14:21:27 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Crawler agent
2025-05-30 14:21:27 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Extractor agent
2025-05-30 14:21:27 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Integrator agent
2025-05-30 14:21:27 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Validator agent
2025-05-30 14:21:27 | INFO | krabulon.agents.orchestrator:initialize:43 | Initializing Agent Orchestrator
2025-05-30 14:21:27 | INFO | krabulon.database.manager:initialize:35 | Initializing Database Manager
2025-05-30 14:21:27 | ERROR | krabulon.database.postgresql:initialize:43 | Failed to initialize PostgreSQL handler: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:21:27 | ERROR | krabulon.database.manager:initialize:44 | Failed to initialize PostgreSQL: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:21:32 | ERROR | krabulon.database.mongodb:initialize:45 | Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839a2c7fdd094455251ba41, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-05-30 14:21:32 | ERROR | krabulon.database.manager:initialize:54 | Failed to initialize MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839a2c7fdd094455251ba41, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-05-30 14:21:32 | ERROR | krabulon.database.neo4j_handler:initialize:38 | Failed to connect to Neo4j: Couldn't connect to localhost:7687 (resolved to ('127.0.0.1:7687',)):
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [Errno 111] Connect call failed ('127.0.0.1', 7687))
2025-05-30 14:21:32 | ERROR | krabulon.database.manager:initialize:64 | Failed to initialize Neo4j: Couldn't connect to localhost:7687 (resolved to ('127.0.0.1:7687',)):
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [Errno 111] Connect call failed ('127.0.0.1', 7687))
2025-05-30 14:21:32 | ERROR | krabulon.database.redis_handler:initialize:38 | Failed to connect to Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379).
2025-05-30 14:21:32 | ERROR | krabulon.database.manager:initialize:74 | Failed to initialize Redis: Error 111 connecting to localhost:6379. Connect call failed ('127.0.0.1', 6379).
2025-05-30 14:21:32 | ERROR | krabulon.database.manager:initialize:84 | Failed to initialize Database Manager: No database handlers could be initialized
2025-05-30 14:21:32 | ERROR | krabulon.agents.orchestrator:initialize:60 | Failed to initialize Agent Orchestrator: No database handlers could be initialized
2025-05-30 14:21:32 | INFO | krabulon.agents.orchestrator:cleanup:67 | Agent Orchestrator cleaned up successfully
2025-05-30 14:24:58 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Planner agent
2025-05-30 14:24:58 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Crawler agent
2025-05-30 14:24:58 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Extractor agent
2025-05-30 14:24:58 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Integrator agent
2025-05-30 14:24:58 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Validator agent
2025-05-30 14:24:58 | INFO | krabulon.agents.orchestrator:initialize:43 | Initializing Agent Orchestrator
2025-05-30 14:24:58 | INFO | krabulon.database.manager:initialize:35 | Initializing Database Manager
2025-05-30 14:24:58 | ERROR | krabulon.database.postgresql:initialize:43 | Failed to initialize PostgreSQL handler: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:24:58 | ERROR | krabulon.database.manager:initialize:44 | Failed to initialize PostgreSQL: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:24:58 | WARNING | krabulon.database.mongodb:_create_indexes:84 | Failed to create MongoDB indexes: Command createIndexes requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command createIndexes requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:24:58 | INFO | krabulon.database.mongodb:initialize:42 | MongoDB connection established successfully
2025-05-30 14:24:58 | INFO | krabulon.database.manager:initialize:52 | MongoDB handler initialized successfully
2025-05-30 14:24:58 | ERROR | krabulon.database.neo4j_handler:initialize:38 | Failed to connect to Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:24:58 | ERROR | krabulon.database.manager:initialize:64 | Failed to initialize Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:24:58 | INFO | krabulon.database.redis_handler:initialize:35 | Redis connection established successfully
2025-05-30 14:24:58 | INFO | krabulon.database.manager:initialize:72 | Redis handler initialized successfully
2025-05-30 14:24:58 | INFO | krabulon.database.manager:initialize:81 | Database Manager initialized with: mongodb, redis
2025-05-30 14:24:58 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:24:59 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:24:59 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:24:59 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | ERROR | krabulon.database.mongodb:get_statistics:195 | Failed to get MongoDB statistics: Command aggregate requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command aggregate requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:52 | Some agents failed health checks
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:55 | planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:55 | crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:55 | extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:55 | integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:55 | validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | WARNING | krabulon.agents.orchestrator:initialize:55 | database: Unknown error
2025-05-30 14:25:00 | INFO | krabulon.agents.orchestrator:initialize:57 | Agent Orchestrator initialized successfully
2025-05-30 14:25:00 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:00 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:01 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:01 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:01 | ERROR | krabulon.database.mongodb:get_statistics:195 | Failed to get MongoDB statistics: Command aggregate requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command aggregate requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:25:01 | INFO | krabulon.database.manager:cleanup:93 | Cleaning up Database Manager
2025-05-30 14:25:01 | INFO | krabulon.database.mongodb:cleanup:58 | MongoDB connection closed
2025-05-30 14:25:01 | INFO | krabulon.database.manager:cleanup:105 | Database Manager cleaned up successfully
2025-05-30 14:25:01 | INFO | krabulon.agents.orchestrator:cleanup:67 | Agent Orchestrator cleaned up successfully
2025-05-30 14:25:12 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Planner agent
2025-05-30 14:25:12 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Crawler agent
2025-05-30 14:25:12 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Extractor agent
2025-05-30 14:25:12 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Integrator agent
2025-05-30 14:25:12 | INFO | krabulon.agents.base_agent:__init__:70 | Initialized Validator agent
2025-05-30 14:25:12 | INFO | krabulon.agents.orchestrator:initialize:43 | Initializing Agent Orchestrator
2025-05-30 14:25:12 | INFO | krabulon.database.manager:initialize:35 | Initializing Database Manager
2025-05-30 14:25:12 | ERROR | krabulon.database.postgresql:initialize:43 | Failed to initialize PostgreSQL handler: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:25:12 | ERROR | krabulon.database.manager:initialize:44 | Failed to initialize PostgreSQL: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:25:12 | WARNING | krabulon.database.mongodb:_create_indexes:84 | Failed to create MongoDB indexes: Command createIndexes requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command createIndexes requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:25:12 | INFO | krabulon.database.mongodb:initialize:42 | MongoDB connection established successfully
2025-05-30 14:25:12 | INFO | krabulon.database.manager:initialize:52 | MongoDB handler initialized successfully
2025-05-30 14:25:12 | ERROR | krabulon.database.neo4j_handler:initialize:38 | Failed to connect to Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:25:12 | ERROR | krabulon.database.manager:initialize:64 | Failed to initialize Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:25:12 | INFO | krabulon.database.redis_handler:initialize:35 | Redis connection established successfully
2025-05-30 14:25:12 | INFO | krabulon.database.manager:initialize:72 | Redis handler initialized successfully
2025-05-30 14:25:12 | INFO | krabulon.database.manager:initialize:81 | Database Manager initialized with: mongodb, redis
2025-05-30 14:25:12 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:12 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | ERROR | krabulon.database.mongodb:get_statistics:195 | Failed to get MongoDB statistics: Command aggregate requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command aggregate requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:52 | Some agents failed health checks
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:55 | planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:55 | crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:55 | extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:55 | integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:55 | validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | WARNING | krabulon.agents.orchestrator:initialize:55 | database: Unknown error
2025-05-30 14:25:14 | INFO | krabulon.agents.orchestrator:initialize:57 | Agent Orchestrator initialized successfully
2025-05-30 14:25:14 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:14 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:15 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:15 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:15 | ERROR | krabulon.agents.base_agent:health_check:217 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:25:15 | ERROR | krabulon.database.mongodb:get_statistics:195 | Failed to get MongoDB statistics: Command aggregate requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command aggregate requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:25:15 | INFO | krabulon.database.manager:cleanup:93 | Cleaning up Database Manager
2025-05-30 14:25:15 | INFO | krabulon.database.mongodb:cleanup:58 | MongoDB connection closed
2025-05-30 14:25:15 | INFO | krabulon.database.manager:cleanup:105 | Database Manager cleaned up successfully
2025-05-30 14:25:15 | INFO | krabulon.agents.orchestrator:cleanup:67 | Agent Orchestrator cleaned up successfully
2025-05-30 14:27:42 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:27:42 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Planner agent
2025-05-30 14:27:42 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Crawler agent
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Extractor agent
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Integrator agent
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:27:43 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Validator agent
2025-05-30 14:27:43 | INFO | krabulon.agents.orchestrator:initialize:43 | Initializing Agent Orchestrator
2025-05-30 14:27:43 | INFO | krabulon.database.manager:initialize:35 | Initializing Database Manager
2025-05-30 14:27:43 | ERROR | krabulon.database.postgresql:initialize:43 | Failed to initialize PostgreSQL handler: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:27:43 | ERROR | krabulon.database.manager:initialize:44 | Failed to initialize PostgreSQL: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:27:43 | ERROR | krabulon.database.mongodb:initialize:51 | MongoDB initialization error: The empty string is not valid username
2025-05-30 14:27:43 | ERROR | krabulon.database.manager:initialize:54 | Failed to initialize MongoDB: The empty string is not valid username
2025-05-30 14:27:43 | ERROR | krabulon.database.neo4j_handler:initialize:39 | Failed to connect to Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:27:43 | ERROR | krabulon.database.manager:initialize:64 | Failed to initialize Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:27:43 | INFO | krabulon.database.redis_handler:initialize:35 | Redis connection established successfully
2025-05-30 14:27:43 | INFO | krabulon.database.manager:initialize:72 | Redis handler initialized successfully
2025-05-30 14:27:43 | INFO | krabulon.database.manager:initialize:81 | Database Manager initialized with: redis
2025-05-30 14:27:43 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:43 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:43 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:52 | Some agents failed health checks
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:55 | planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:55 | crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:55 | extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:55 | integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:55 | validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | WARNING | krabulon.agents.orchestrator:initialize:55 | database: Unknown error
2025-05-30 14:27:44 | INFO | krabulon.agents.orchestrator:initialize:57 | Agent Orchestrator initialized successfully
2025-05-30 14:27:44 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:44 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:45 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:27:45 | INFO | krabulon.database.manager:cleanup:93 | Cleaning up Database Manager
2025-05-30 14:27:45 | INFO | krabulon.database.manager:cleanup:105 | Database Manager cleaned up successfully
2025-05-30 14:27:45 | INFO | krabulon.agents.orchestrator:cleanup:67 | Agent Orchestrator cleaned up successfully
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Planner agent
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Crawler agent
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Extractor agent
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Integrator agent
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:_initialize_llm:83 | Initializing OpenAI LLM
2025-05-30 14:28:36 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Validator agent
2025-05-30 14:28:36 | INFO | krabulon.agents.orchestrator:initialize:43 | Initializing Agent Orchestrator
2025-05-30 14:28:36 | INFO | krabulon.database.manager:initialize:35 | Initializing Database Manager
2025-05-30 14:28:36 | ERROR | krabulon.database.postgresql:initialize:43 | Failed to initialize PostgreSQL handler: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:28:36 | ERROR | krabulon.database.manager:initialize:44 | Failed to initialize PostgreSQL: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:28:36 | ERROR | krabulon.database.mongodb:initialize:51 | MongoDB initialization error: The empty string is not valid username
2025-05-30 14:28:36 | ERROR | krabulon.database.manager:initialize:54 | Failed to initialize MongoDB: The empty string is not valid username
2025-05-30 14:28:37 | ERROR | krabulon.database.neo4j_handler:initialize:39 | Failed to connect to Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:28:37 | ERROR | krabulon.database.manager:initialize:64 | Failed to initialize Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:28:37 | INFO | krabulon.database.redis_handler:initialize:35 | Redis connection established successfully
2025-05-30 14:28:37 | INFO | krabulon.database.manager:initialize:72 | Redis handler initialized successfully
2025-05-30 14:28:37 | INFO | krabulon.database.manager:initialize:81 | Database Manager initialized with: redis
2025-05-30 14:28:37 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:37 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:37 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:37 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:52 | Some agents failed health checks
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:55 | planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:55 | crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:55 | extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:55 | integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:55 | validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | WARNING | krabulon.agents.orchestrator:initialize:55 | database: Unknown error
2025-05-30 14:28:38 | INFO | krabulon.agents.orchestrator:initialize:57 | Agent Orchestrator initialized successfully
2025-05-30 14:28:38 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Planner: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Crawler: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Extractor: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Integrator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | ERROR | krabulon.agents.base_agent:health_check:229 | Health check failed for Validator: Error code: 401 - {'error': {'message': 'Incorrect API key provided: ${OPENAI*****KEY}. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-30 14:28:38 | INFO | krabulon.database.manager:cleanup:93 | Cleaning up Database Manager
2025-05-30 14:28:38 | INFO | krabulon.database.manager:cleanup:105 | Database Manager cleaned up successfully
2025-05-30 14:28:38 | INFO | krabulon.agents.orchestrator:cleanup:67 | Agent Orchestrator cleaned up successfully
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:_initialize_llm:91 | Initializing LM Studio LLM
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Planner agent
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:_initialize_llm:91 | Initializing LM Studio LLM
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Crawler agent
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:_initialize_llm:91 | Initializing LM Studio LLM
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Extractor agent
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:_initialize_llm:91 | Initializing LM Studio LLM
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Integrator agent
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:_initialize_llm:91 | Initializing LM Studio LLM
2025-05-30 14:54:12 | INFO | krabulon.agents.base_agent:__init__:71 | Initialized Validator agent
2025-05-30 14:54:12 | INFO | krabulon.agents.orchestrator:initialize:43 | Initializing Agent Orchestrator
2025-05-30 14:54:12 | INFO | krabulon.database.manager:initialize:35 | Initializing Database Manager
2025-05-30 14:54:12 | ERROR | krabulon.database.postgresql:initialize:43 | Failed to initialize PostgreSQL handler: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:54:12 | ERROR | krabulon.database.manager:initialize:44 | Failed to initialize PostgreSQL: [Errno 111] Connect call failed ('127.0.0.1', 5432)
2025-05-30 14:54:12 | WARNING | krabulon.database.mongodb:_create_indexes:89 | Failed to create MongoDB indexes: Command createIndexes requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command createIndexes requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:54:12 | INFO | krabulon.database.mongodb:initialize:47 | MongoDB connection established successfully
2025-05-30 14:54:12 | INFO | krabulon.database.manager:initialize:52 | MongoDB handler initialized successfully
2025-05-30 14:54:12 | ERROR | krabulon.database.neo4j_handler:initialize:39 | Failed to connect to Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:54:12 | ERROR | krabulon.database.manager:initialize:64 | Failed to initialize Neo4j: {code: Neo.ClientError.Security.Unauthorized} {message: The client is unauthorized due to authentication failure.}
2025-05-30 14:54:12 | INFO | krabulon.database.redis_handler:initialize:35 | Redis connection established successfully
2025-05-30 14:54:12 | INFO | krabulon.database.manager:initialize:72 | Redis handler initialized successfully
2025-05-30 14:54:12 | INFO | krabulon.database.manager:initialize:81 | Database Manager initialized with: mongodb, redis
2025-05-30 14:54:13 | ERROR | krabulon.database.mongodb:get_statistics:200 | Failed to get MongoDB statistics: Command aggregate requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command aggregate requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:54:13 | WARNING | krabulon.agents.orchestrator:initialize:52 | Some agents failed health checks
2025-05-30 14:54:13 | WARNING | krabulon.agents.orchestrator:initialize:55 | database: Unknown error
2025-05-30 14:54:13 | INFO | krabulon.agents.orchestrator:initialize:57 | Agent Orchestrator initialized successfully
2025-05-30 14:54:13 | ERROR | krabulon.agents.lm_studio_adapter:predict:81 | LM Studio request error: Event loop is closed
2025-05-30 14:54:13 | ERROR | krabulon.agents.lm_studio_adapter:predict:81 | LM Studio request error: Event loop is closed
2025-05-30 14:54:13 | ERROR | krabulon.agents.lm_studio_adapter:predict:81 | LM Studio request error: Event loop is closed
2025-05-30 14:54:13 | ERROR | krabulon.agents.lm_studio_adapter:predict:81 | LM Studio request error: Event loop is closed
2025-05-30 14:54:13 | ERROR | krabulon.agents.lm_studio_adapter:predict:81 | LM Studio request error: Event loop is closed
2025-05-30 14:54:13 | ERROR | krabulon.database.mongodb:get_statistics:200 | Failed to get MongoDB statistics: Command aggregate requires authentication, full error: {'ok': 0.0, 'errmsg': 'Command aggregate requires authentication', 'code': 13, 'codeName': 'Unauthorized'}
2025-05-30 14:54:13 | INFO | krabulon.database.manager:cleanup:93 | Cleaning up Database Manager
2025-05-30 14:54:13 | INFO | krabulon.database.mongodb:cleanup:63 | MongoDB connection closed
2025-05-30 14:54:13 | INFO | krabulon.database.manager:cleanup:105 | Database Manager cleaned up successfully
2025-05-30 14:54:13 | INFO | krabulon.agents.orchestrator:cleanup:67 | Agent Orchestrator cleaned up successfully
