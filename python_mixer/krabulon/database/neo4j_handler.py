
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Neo4j graph database handler for HVAC equipment relationships."""

from typing import Dict, List, Any, Optional
from datetime import datetime
from neo4j import AsyncGraphDatabase
from loguru import logger

from config import settings


class Neo4jHandler:
    """Neo4j handler for storing and querying HVAC equipment relationships."""
    
    def __init__(self):
        self.driver = None
        self.connected = False
    
    async def initialize(self):
        """Initialize Neo4j connection."""
        try:
            neo4j_uri = f"bolt://{settings.database.neo4j_host}:{settings.database.neo4j_port}"
            self.driver = AsyncGraphDatabase.driver(
                neo4j_uri,
                auth=(settings.database.neo4j_username, settings.database.neo4j_password)
            )
            
            # Test connection
            async with self.driver.session() as session:
                result = await session.run("RETURN 1 as test")
                await result.single()
            
            # Create constraints and indexes
            await self._create_constraints()
            
            self.connected = True
            logger.info("Neo4j connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            self.connected = False
            raise
    
    async def cleanup(self):
        """Cleanup Neo4j connection."""
        if self.driver:
            await self.driver.close()
            self.connected = False
            logger.info("Neo4j connection closed")
    
    async def _create_constraints(self):
        """Create necessary constraints and indexes."""
        try:
            async with self.driver.session() as session:
                # Create constraints
                constraints = [
                    "CREATE CONSTRAINT equipment_model IF NOT EXISTS FOR (e:Equipment) REQUIRE (e.manufacturer, e.model) IS UNIQUE",
                    "CREATE CONSTRAINT manufacturer_name IF NOT EXISTS FOR (m:Manufacturer) REQUIRE m.name IS UNIQUE",
                    "CREATE CONSTRAINT feature_name IF NOT EXISTS FOR (f:Feature) REQUIRE f.name IS UNIQUE"
                ]
                
                for constraint in constraints:
                    try:
                        await session.run(constraint)
                    except Exception as e:
                        logger.debug(f"Constraint already exists or failed: {e}")
                
                logger.info("Neo4j constraints created successfully")
                
        except Exception as e:
            logger.warning(f"Failed to create Neo4j constraints: {e}")
    
    async def save_equipment_relationships(self, equipment_data: Dict[str, Any]) -> str:
        """Save equipment and its relationships to Neo4j."""
        try:
            async with self.driver.session() as session:
                # Create equipment node
                equipment_result = await session.run("""
                    MERGE (e:Equipment {
                        manufacturer: $manufacturer,
                        model: $model
                    })
                    SET e.type = $type,
                        e.series = $series,
                        e.release_year = $release_year,
                        e.cooling_capacity = $cooling_capacity,
                        e.heating_capacity = $heating_capacity,
                        e.energy_class = $energy_class,
                        e.confidence_score = $confidence_score,
                        e.last_updated = $last_updated
                    RETURN id(e) as equipment_id
                """, {
                    "manufacturer": equipment_data["basic_info"]["manufacturer"],
                    "model": equipment_data["basic_info"]["model"],
                    "type": equipment_data["basic_info"]["type"],
                    "series": equipment_data["basic_info"].get("series", ""),
                    "release_year": equipment_data["basic_info"].get("release_year", 0),
                    "cooling_capacity": equipment_data["technical_specs"]["cooling_capacity"]["value"],
                    "heating_capacity": equipment_data["technical_specs"]["heating_capacity"]["value"],
                    "energy_class": equipment_data["technical_specs"]["energy_efficiency"]["energy_class"],
                    "confidence_score": equipment_data["metadata"]["confidence_score"],
                    "last_updated": datetime.now().isoformat()
                })
                
                equipment_id = await equipment_result.single()
                
                # Create manufacturer relationship
                await session.run("""
                    MATCH (e:Equipment) WHERE id(e) = $equipment_id
                    MERGE (m:Manufacturer {name: $manufacturer})
                    MERGE (m)-[:PRODUCES]->(e)
                """, {
                    "equipment_id": equipment_id["equipment_id"],
                    "manufacturer": equipment_data["basic_info"]["manufacturer"]
                })
                
                # Create feature relationships
                for feature in equipment_data.get("features", []):
                    await session.run("""
                        MATCH (e:Equipment) WHERE id(e) = $equipment_id
                        MERGE (f:Feature {name: $feature})
                        MERGE (e)-[:HAS_FEATURE]->(f)
                    """, {
                        "equipment_id": equipment_id["equipment_id"],
                        "feature": feature
                    })
                
                logger.info(f"Equipment relationships saved to Neo4j: {equipment_data['basic_info']['manufacturer']} {equipment_data['basic_info']['model']}")
                return str(equipment_id["equipment_id"])
                
        except Exception as e:
            logger.error(f"Failed to save equipment relationships to Neo4j: {e}")
            raise
    
    async def get_equipment_relationships(self, manufacturer: str, model: str) -> Dict[str, Any]:
        """Get equipment and its relationships."""
        try:
            async with self.driver.session() as session:
                result = await session.run("""
                    MATCH (m:Manufacturer)-[:PRODUCES]->(e:Equipment {manufacturer: $manufacturer, model: $model})
                    OPTIONAL MATCH (e)-[:HAS_FEATURE]->(f:Feature)
                    RETURN e, collect(f.name) as features
                """, {
                    "manufacturer": manufacturer,
                    "model": model
                })
                
                record = await result.single()
                if record:
                    equipment = dict(record["e"])
                    equipment["features"] = record["features"]
                    return equipment
                
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get equipment relationships from Neo4j: {e}")
            raise
    
    async def find_similar_equipment(self, equipment_type: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Find similar equipment based on type and features."""
        try:
            async with self.driver.session() as session:
                result = await session.run("""
                    MATCH (e:Equipment {type: $equipment_type})-[:HAS_FEATURE]->(f:Feature)
                    WITH e, collect(f.name) as features, count(f) as feature_count
                    ORDER BY feature_count DESC
                    LIMIT $limit
                    RETURN e, features
                """, {
                    "equipment_type": equipment_type,
                    "limit": limit
                })
                
                equipment_list = []
                async for record in result:
                    equipment = dict(record["e"])
                    equipment["features"] = record["features"]
                    equipment_list.append(equipment)
                
                return equipment_list
                
        except Exception as e:
            logger.error(f"Failed to find similar equipment in Neo4j: {e}")
            raise
    
    async def get_manufacturer_statistics(self) -> List[Dict[str, Any]]:
        """Get statistics by manufacturer."""
        try:
            async with self.driver.session() as session:
                result = await session.run("""
                    MATCH (m:Manufacturer)-[:PRODUCES]->(e:Equipment)
                    RETURN m.name as manufacturer, 
                           count(e) as equipment_count,
                           avg(e.confidence_score) as avg_confidence,
                           collect(DISTINCT e.type) as types
                    ORDER BY equipment_count DESC
                """)
                
                stats = []
                async for record in result:
                    stats.append({
                        "manufacturer": record["manufacturer"],
                        "equipment_count": record["equipment_count"],
                        "avg_confidence": record["avg_confidence"],
                        "types": record["types"]
                    })
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get manufacturer statistics from Neo4j: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Neo4j connection."""
        try:
            if not self.connected or not self.driver:
                return {"healthy": False, "error": "Not connected"}
            
            async with self.driver.session() as session:
                result = await session.run("RETURN 1 as test")
                await result.single()
                
                # Get node counts
                count_result = await session.run("""
                    MATCH (e:Equipment) 
                    RETURN count(e) as equipment_count
                """)
                count_record = await count_result.single()
                
                return {
                    "healthy": True,
                    "equipment_count": count_record["equipment_count"],
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
