
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""PostgreSQL database handler for HVAC equipment data."""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

import asyncpg
import psycopg2
from psycopg2.extras import RealDictCursor

from config import settings
from models import HVACEquipment


class PostgreSQLHandler:
    """PostgreSQL database handler for HVAC equipment data."""
    
    def __init__(self):
        self.connection_pool = None
        self.sync_connection = None
        
    async def initialize(self):
        """Initialize PostgreSQL connection pool."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                host=settings.database.postgresql_host,
                port=settings.database.postgresql_port,
                database=settings.database.postgresql_database,
                user=settings.database.postgresql_username,
                password=settings.database.postgresql_password,
                min_size=5,
                max_size=settings.database.postgresql_pool_size
            )
            
            # Create tables if they don't exist
            await self._create_tables()
            
            logger.info("PostgreSQL handler initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL handler: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup database connections."""
        if self.connection_pool:
            await self.connection_pool.close()
            logger.info("PostgreSQL connection pool closed")
    
    async def _create_tables(self):
        """Create necessary tables for HVAC equipment data."""
        
        create_tables_sql = """
        -- Main equipment table
        CREATE TABLE IF NOT EXISTS hvac_equipment (
            id VARCHAR(50) PRIMARY KEY,
            manufacturer VARCHAR(100) NOT NULL,
            model VARCHAR(100) NOT NULL,
            type VARCHAR(50) NOT NULL,
            series VARCHAR(100),
            release_year INTEGER,
            source_url TEXT,
            extraction_date TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            confidence_score FLOAT DEFAULT 0.0,
            extractor_version VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(manufacturer, model, type)
        );
        
        -- Technical specifications table
        CREATE TABLE IF NOT EXISTS technical_specs (
            id SERIAL PRIMARY KEY,
            equipment_id VARCHAR(50) REFERENCES hvac_equipment(id) ON DELETE CASCADE,
            cooling_capacity_value FLOAT,
            cooling_capacity_unit VARCHAR(20),
            heating_capacity_value FLOAT,
            heating_capacity_unit VARCHAR(20),
            cooling_eer FLOAT,
            heating_cop FLOAT,
            seer FLOAT,
            scop FLOAT,
            energy_class VARCHAR(10),
            width FLOAT,
            height FLOAT,
            depth FLOAT,
            dimensions_unit VARCHAR(10),
            weight_value FLOAT,
            weight_unit VARCHAR(10),
            refrigerant_type VARCHAR(20),
            refrigerant_charge FLOAT,
            refrigerant_unit VARCHAR(10),
            power_supply VARCHAR(50),
            cooling_power FLOAT,
            heating_power FLOAT,
            electrical_unit VARCHAR(10),
            airflow_value FLOAT,
            airflow_unit VARCHAR(20),
            noise_indoor FLOAT,
            noise_outdoor FLOAT,
            noise_unit VARCHAR(10),
            cooling_temp_min FLOAT,
            cooling_temp_max FLOAT,
            heating_temp_min FLOAT,
            heating_temp_max FLOAT,
            temp_unit VARCHAR(10),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Features table
        CREATE TABLE IF NOT EXISTS equipment_features (
            id SERIAL PRIMARY KEY,
            equipment_id VARCHAR(50) REFERENCES hvac_equipment(id) ON DELETE CASCADE,
            feature_name VARCHAR(200) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Certifications table
        CREATE TABLE IF NOT EXISTS equipment_certifications (
            id SERIAL PRIMARY KEY,
            equipment_id VARCHAR(50) REFERENCES hvac_equipment(id) ON DELETE CASCADE,
            certification_name VARCHAR(200) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Documentation table
        CREATE TABLE IF NOT EXISTS equipment_documentation (
            id SERIAL PRIMARY KEY,
            equipment_id VARCHAR(50) REFERENCES hvac_equipment(id) ON DELETE CASCADE,
            doc_type VARCHAR(50) NOT NULL, -- manual, datasheet, installation_guide
            doc_url TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_hvac_equipment_manufacturer ON hvac_equipment(manufacturer);
        CREATE INDEX IF NOT EXISTS idx_hvac_equipment_type ON hvac_equipment(type);
        CREATE INDEX IF NOT EXISTS idx_hvac_equipment_model ON hvac_equipment(model);
        CREATE INDEX IF NOT EXISTS idx_technical_specs_equipment_id ON technical_specs(equipment_id);
        CREATE INDEX IF NOT EXISTS idx_equipment_features_equipment_id ON equipment_features(equipment_id);
        CREATE INDEX IF NOT EXISTS idx_equipment_certifications_equipment_id ON equipment_certifications(equipment_id);
        CREATE INDEX IF NOT EXISTS idx_equipment_documentation_equipment_id ON equipment_documentation(equipment_id);
        """
        
        async with self.connection_pool.acquire() as connection:
            await connection.execute(create_tables_sql)
            logger.info("PostgreSQL tables created/verified successfully")
    
    async def save_equipment(self, equipment_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Save HVAC equipment data to PostgreSQL.
        
        Args:
            equipment_data: List of equipment dictionaries
            
        Returns:
            Save operation results
        """
        if not equipment_data:
            return {"saved": 0, "updated": 0, "errors": 0}
        
        saved_count = 0
        updated_count = 0
        error_count = 0
        errors = []
        
        async with self.connection_pool.acquire() as connection:
            async with connection.transaction():
                for equipment in equipment_data:
                    try:
                        result = await self._save_single_equipment(connection, equipment)
                        if result == "saved":
                            saved_count += 1
                        elif result == "updated":
                            updated_count += 1
                    except Exception as e:
                        error_count += 1
                        errors.append(f"Equipment {equipment.get('id', 'unknown')}: {str(e)}")
                        logger.error(f"Failed to save equipment: {e}")
        
        logger.info(f"PostgreSQL save completed: {saved_count} saved, {updated_count} updated, {error_count} errors")
        
        return {
            "saved": saved_count,
            "updated": updated_count,
            "errors": error_count,
            "error_details": errors
        }
    
    async def _save_single_equipment(self, connection, equipment: Dict[str, Any]) -> str:
        """Save a single equipment item."""
        
        basic_info = equipment.get("basic_info", {})
        tech_specs = equipment.get("technical_specs", {})
        metadata = equipment.get("metadata", {})
        
        equipment_id = equipment.get("id")
        if not equipment_id:
            # Generate ID if not present
            import hashlib
            id_string = f"{basic_info.get('manufacturer', '')}_{basic_info.get('model', '')}_{basic_info.get('type', '')}"
            equipment_id = hashlib.md5(id_string.encode()).hexdigest()[:12]
        
        # Check if equipment exists
        existing = await connection.fetchrow(
            "SELECT id FROM hvac_equipment WHERE id = $1",
            equipment_id
        )
        
        operation = "updated" if existing else "saved"
        
        # Insert/update main equipment record
        await connection.execute("""
            INSERT INTO hvac_equipment (
                id, manufacturer, model, type, series, release_year,
                source_url, extraction_date, confidence_score, extractor_version
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (id) DO UPDATE SET
                manufacturer = EXCLUDED.manufacturer,
                model = EXCLUDED.model,
                type = EXCLUDED.type,
                series = EXCLUDED.series,
                release_year = EXCLUDED.release_year,
                source_url = EXCLUDED.source_url,
                extraction_date = EXCLUDED.extraction_date,
                confidence_score = EXCLUDED.confidence_score,
                extractor_version = EXCLUDED.extractor_version,
                last_updated = CURRENT_TIMESTAMP
        """, 
            equipment_id,
            basic_info.get("manufacturer"),
            basic_info.get("model"),
            basic_info.get("type"),
            basic_info.get("series"),
            basic_info.get("release_year"),
            metadata.get("source_url"),
            datetime.fromisoformat(metadata.get("extraction_date", datetime.now().isoformat())),
            equipment.get("confidence_score", 0.0),
            metadata.get("extractor_version", "1.0.0")
        )
        
        # Delete existing related records if updating
        if existing:
            await connection.execute("DELETE FROM technical_specs WHERE equipment_id = $1", equipment_id)
            await connection.execute("DELETE FROM equipment_features WHERE equipment_id = $1", equipment_id)
            await connection.execute("DELETE FROM equipment_certifications WHERE equipment_id = $1", equipment_id)
            await connection.execute("DELETE FROM equipment_documentation WHERE equipment_id = $1", equipment_id)
        
        # Insert technical specifications
        if tech_specs:
            await self._save_technical_specs(connection, equipment_id, tech_specs)
        
        # Insert features
        features = equipment.get("features", [])
        for feature in features:
            await connection.execute(
                "INSERT INTO equipment_features (equipment_id, feature_name) VALUES ($1, $2)",
                equipment_id, feature
            )
        
        # Insert certifications
        certifications = equipment.get("certifications", [])
        for certification in certifications:
            await connection.execute(
                "INSERT INTO equipment_certifications (equipment_id, certification_name) VALUES ($1, $2)",
                equipment_id, certification
            )
        
        # Insert documentation
        documentation = equipment.get("documentation", {})
        for doc_type, urls in documentation.items():
            if isinstance(urls, list):
                for url in urls:
                    await connection.execute(
                        "INSERT INTO equipment_documentation (equipment_id, doc_type, doc_url) VALUES ($1, $2, $3)",
                        equipment_id, doc_type, url
                    )
        
        return operation
    
    async def _save_technical_specs(self, connection, equipment_id: str, tech_specs: Dict[str, Any]):
        """Save technical specifications."""
        
        # Extract values from nested dictionaries
        cooling_capacity = tech_specs.get("cooling_capacity", {})
        heating_capacity = tech_specs.get("heating_capacity", {})
        energy_efficiency = tech_specs.get("energy_efficiency", {})
        dimensions = tech_specs.get("dimensions", {})
        weight = tech_specs.get("weight", {})
        refrigerant = tech_specs.get("refrigerant", {})
        electrical = tech_specs.get("electrical", {})
        airflow = tech_specs.get("airflow", {})
        noise_level = tech_specs.get("noise_level", {})
        operating_range = tech_specs.get("operating_range", {})
        
        await connection.execute("""
            INSERT INTO technical_specs (
                equipment_id, cooling_capacity_value, cooling_capacity_unit,
                heating_capacity_value, heating_capacity_unit,
                cooling_eer, heating_cop, seer, scop, energy_class,
                width, height, depth, dimensions_unit,
                weight_value, weight_unit,
                refrigerant_type, refrigerant_charge, refrigerant_unit,
                power_supply, cooling_power, heating_power, electrical_unit,
                airflow_value, airflow_unit,
                noise_indoor, noise_outdoor, noise_unit,
                cooling_temp_min, cooling_temp_max,
                heating_temp_min, heating_temp_max, temp_unit
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                $11, $12, $13, $14, $15, $16, $17, $18, $19,
                $20, $21, $22, $23, $24, $25, $26, $27, $28,
                $29, $30, $31, $32, $33
            )
        """,
            equipment_id,
            cooling_capacity.get("value"), cooling_capacity.get("unit"),
            heating_capacity.get("value"), heating_capacity.get("unit"),
            energy_efficiency.get("cooling_eer"), energy_efficiency.get("heating_cop"),
            energy_efficiency.get("seer"), energy_efficiency.get("scop"),
            energy_efficiency.get("energy_class"),
            dimensions.get("width"), dimensions.get("height"), dimensions.get("depth"),
            dimensions.get("unit"),
            weight.get("value"), weight.get("unit"),
            refrigerant.get("type"), refrigerant.get("charge"), refrigerant.get("unit"),
            electrical.get("power_supply"), electrical.get("cooling_power"),
            electrical.get("heating_power"), electrical.get("unit"),
            airflow.get("value"), airflow.get("unit"),
            noise_level.get("indoor"), noise_level.get("outdoor"), noise_level.get("unit"),
            operating_range.get("cooling", {}).get("min"),
            operating_range.get("cooling", {}).get("max"),
            operating_range.get("heating", {}).get("min"),
            operating_range.get("heating", {}).get("max"),
            operating_range.get("unit")
        )
    
    async def get_equipment(
        self,
        manufacturer: Optional[str] = None,
        equipment_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Retrieve HVAC equipment data from PostgreSQL."""
        
        query = """
            SELECT e.*, ts.* FROM hvac_equipment e
            LEFT JOIN technical_specs ts ON e.id = ts.equipment_id
            WHERE 1=1
        """
        params = []
        param_count = 0
        
        if manufacturer:
            param_count += 1
            query += f" AND e.manufacturer ILIKE ${param_count}"
            params.append(f"%{manufacturer}%")
        
        if equipment_type:
            param_count += 1
            query += f" AND e.type = ${param_count}"
            params.append(equipment_type)
        
        query += f" ORDER BY e.created_at DESC LIMIT ${param_count + 1} OFFSET ${param_count + 2}"
        params.extend([limit, offset])
        
        async with self.connection_pool.acquire() as connection:
            rows = await connection.fetch(query, *params)
            
            equipment_list = []
            for row in rows:
                equipment_dict = dict(row)
                
                # Get features
                features = await connection.fetch(
                    "SELECT feature_name FROM equipment_features WHERE equipment_id = $1",
                    equipment_dict["id"]
                )
                equipment_dict["features"] = [f["feature_name"] for f in features]
                
                # Get certifications
                certifications = await connection.fetch(
                    "SELECT certification_name FROM equipment_certifications WHERE equipment_id = $1",
                    equipment_dict["id"]
                )
                equipment_dict["certifications"] = [c["certification_name"] for c in certifications]
                
                equipment_list.append(equipment_dict)
        
        return equipment_list
    
    async def search_equipment(self, search_term: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search equipment by text."""
        
        query = """
            SELECT e.*, ts.* FROM hvac_equipment e
            LEFT JOIN technical_specs ts ON e.id = ts.equipment_id
            WHERE e.manufacturer ILIKE $1 
               OR e.model ILIKE $1 
               OR e.type ILIKE $1
               OR e.series ILIKE $1
            ORDER BY e.confidence_score DESC, e.created_at DESC
            LIMIT $2
        """
        
        search_pattern = f"%{search_term}%"
        
        async with self.connection_pool.acquire() as connection:
            rows = await connection.fetch(query, search_pattern, limit)
            return [dict(row) for row in rows]
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        
        async with self.connection_pool.acquire() as connection:
            stats = {}
            
            # Total equipment count
            stats["total_equipment"] = await connection.fetchval(
                "SELECT COUNT(*) FROM hvac_equipment"
            )
            
            # Equipment by manufacturer
            manufacturer_stats = await connection.fetch("""
                SELECT manufacturer, COUNT(*) as count 
                FROM hvac_equipment 
                GROUP BY manufacturer 
                ORDER BY count DESC 
                LIMIT 10
            """)
            stats["by_manufacturer"] = [dict(row) for row in manufacturer_stats]
            
            # Equipment by type
            type_stats = await connection.fetch("""
                SELECT type, COUNT(*) as count 
                FROM hvac_equipment 
                GROUP BY type 
                ORDER BY count DESC
            """)
            stats["by_type"] = [dict(row) for row in type_stats]
            
            # Average confidence score
            stats["average_confidence"] = await connection.fetchval(
                "SELECT AVG(confidence_score) FROM hvac_equipment"
            ) or 0.0
            
            # Recent additions
            stats["recent_additions"] = await connection.fetchval(
                "SELECT COUNT(*) FROM hvac_equipment WHERE created_at > NOW() - INTERVAL '7 days'"
            )
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on PostgreSQL connection."""
        
        try:
            async with self.connection_pool.acquire() as connection:
                # Test basic query
                result = await connection.fetchval("SELECT 1")
                
                # Check table existence
                tables = await connection.fetch("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name LIKE 'hvac_%'
                """)
                
                return {
                    "status": "healthy",
                    "connection": "ok",
                    "tables_found": len(tables),
                    "pool_size": self.connection_pool.get_size(),
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
