
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Database Manager for coordinating multiple database backends."""

from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .postgresql import <PERSON>greS<PERSON><PERSON><PERSON><PERSON>
from .mongodb import <PERSON>go<PERSON><PERSON><PERSON><PERSON><PERSON>
from .neo4j_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .redis_handler import <PERSON><PERSON><PERSON>andler
from config import settings





class DatabaseManager:
    """Manager for coordinating multiple database backends."""
    
    def __init__(self):
        self.postgresql: Optional[PostgreSQLHandler] = None
        self.mongodb: Optional[MongoDBHandler] = None
        self.neo4j: Optional[Neo4jHandler] = None
        self.redis: Optional[RedisHandler] = None

        self.enabled_databases = []
        self._initialized = False
    
    async def initialize(self):
        """Initialize all configured database handlers."""
        if self._initialized:
            return
        
        try:
            logger.info("Initializing Database Manager")
            
            # Initialize PostgreSQL
            try:
                self.postgresql = PostgreSQLHandler()
                await self.postgresql.initialize()
                self.enabled_databases.append("postgresql")
                logger.info("PostgreSQL handler initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize PostgreSQL: {e}")
                self.postgresql = None
            
            # Initialize MongoDB
            try:
                self.mongodb = MongoDBHandler()
                await self.mongodb.initialize()
                self.enabled_databases.append("mongodb")
                logger.info("MongoDB handler initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize MongoDB: {e}")
                self.mongodb = None
            
            # Initialize Neo4j
            try:
                self.neo4j = Neo4jHandler()
                await self.neo4j.initialize()
                self.enabled_databases.append("neo4j")
                logger.info("Neo4j handler initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Neo4j: {e}")
                self.neo4j = None

            # Initialize Redis
            try:
                self.redis = RedisHandler()
                await self.redis.initialize()
                self.enabled_databases.append("redis")
                logger.info("Redis handler initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Redis: {e}")
                self.redis = None
            
            if not self.enabled_databases:
                raise RuntimeError("No database handlers could be initialized")
            
            self._initialized = True
            logger.info(f"Database Manager initialized with: {', '.join(self.enabled_databases)}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Database Manager: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup all database connections."""
        if not self._initialized:
            return
        
        try:
            logger.info("Cleaning up Database Manager")
            
            if self.postgresql:
                await self.postgresql.cleanup()
            
            if self.mongodb:
                await self.mongodb.cleanup()
            
            if self.neo4j:
                await self.neo4j.cleanup()
            
            self._initialized = False
            logger.info("Database Manager cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during database cleanup: {e}")
    
    async def save_equipment_to_all(
        self,
        equipment_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Save equipment data to all available databases."""
        
        if not self._initialized:
            raise RuntimeError("Database Manager not initialized")
        
        results = {
            "total_items": len(equipment_data),
            "databases": {},
            "summary": {
                "successful_databases": 0,
                "failed_databases": 0,
                "total_saved": 0,
                "total_updated": 0,
                "total_errors": 0
            }
        }
        
        # Save to PostgreSQL
        if self.postgresql:
            try:
                pg_result = await self.postgresql.save_equipment(equipment_data)
                results["databases"]["postgresql"] = pg_result
                results["summary"]["successful_databases"] += 1
                results["summary"]["total_saved"] += pg_result.get("saved", 0)
                results["summary"]["total_updated"] += pg_result.get("updated", 0)
                results["summary"]["total_errors"] += pg_result.get("errors", 0)
            except Exception as e:
                logger.error(f"PostgreSQL save failed: {e}")
                results["databases"]["postgresql"] = {"error": str(e)}
                results["summary"]["failed_databases"] += 1
        
        # Save to MongoDB
        if self.mongodb:
            try:
                mongo_result = await self.mongodb.save_equipment(equipment_data)
                results["databases"]["mongodb"] = mongo_result
                results["summary"]["successful_databases"] += 1
                results["summary"]["total_saved"] += mongo_result.get("saved", 0)
                results["summary"]["total_updated"] += mongo_result.get("updated", 0)
                results["summary"]["total_errors"] += mongo_result.get("errors", 0)
            except Exception as e:
                logger.error(f"MongoDB save failed: {e}")
                results["databases"]["mongodb"] = {"error": str(e)}
                results["summary"]["failed_databases"] += 1
        
        # Save to Neo4j
        if self.neo4j:
            try:
                neo4j_result = await self.neo4j.save_equipment(equipment_data)
                results["databases"]["neo4j"] = neo4j_result
                results["summary"]["successful_databases"] += 1
                results["summary"]["total_saved"] += neo4j_result.get("saved", 0)
                results["summary"]["total_updated"] += neo4j_result.get("updated", 0)
                results["summary"]["total_errors"] += neo4j_result.get("errors", 0)
            except Exception as e:
                logger.error(f"Neo4j save failed: {e}")
                results["databases"]["neo4j"] = {"error": str(e)}
                results["summary"]["failed_databases"] += 1
        
        logger.info(f"Equipment saved to {results['summary']['successful_databases']} databases")
        return results
    
    async def get_equipment(
        self,
        manufacturer: Optional[str] = None,
        equipment_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
        database: str = "postgresql"
    ) -> List[Dict[str, Any]]:
        """Retrieve equipment data from specified database."""
        
        if not self._initialized:
            raise RuntimeError("Database Manager not initialized")
        
        if database == "postgresql" and self.postgresql:
            return await self.postgresql.get_equipment(
                manufacturer=manufacturer,
                equipment_type=equipment_type,
                limit=limit,
                offset=offset
            )
        elif database == "mongodb" and self.mongodb:
            # MongoDB implementation would go here
            logger.warning("MongoDB get_equipment not implemented")
            return []
        elif database == "neo4j" and self.neo4j:
            # Neo4j implementation would go here
            logger.warning("Neo4j get_equipment not implemented")
            return []
        else:
            raise ValueError(f"Database '{database}' not available or not supported")
    
    async def search_equipment(
        self,
        search_term: str,
        limit: int = 50,
        database: str = "postgresql"
    ) -> List[Dict[str, Any]]:
        """Search equipment data in specified database."""
        
        if not self._initialized:
            raise RuntimeError("Database Manager not initialized")
        
        if database == "postgresql" and self.postgresql:
            return await self.postgresql.search_equipment(search_term, limit)
        elif database == "mongodb" and self.mongodb:
            # MongoDB implementation would go here
            logger.warning("MongoDB search_equipment not implemented")
            return []
        elif database == "neo4j" and self.neo4j:
            # Neo4j implementation would go here
            logger.warning("Neo4j search_equipment not implemented")
            return []
        else:
            raise ValueError(f"Database '{database}' not available or not supported")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        
        if not self._initialized:
            raise RuntimeError("Database Manager not initialized")
        
        stats = {
            "enabled_databases": self.enabled_databases,
            "database_stats": {},
            "summary": {
                "total_equipment": 0,
                "average_confidence": 0.0,
                "recent_additions": 0
            }
        }
        
        # Get PostgreSQL statistics
        if self.postgresql:
            try:
                pg_stats = await self.postgresql.get_statistics()
                stats["database_stats"]["postgresql"] = pg_stats
                
                # Use PostgreSQL as primary for summary
                stats["summary"]["total_equipment"] = pg_stats.get("total_equipment", 0)
                stats["summary"]["average_confidence"] = pg_stats.get("average_confidence", 0.0)
                stats["summary"]["recent_additions"] = pg_stats.get("recent_additions", 0)
                
            except Exception as e:
                logger.error(f"Failed to get PostgreSQL statistics: {e}")
                stats["database_stats"]["postgresql"] = {"error": str(e)}
        
        # Get MongoDB statistics (placeholder)
        if self.mongodb:
            stats["database_stats"]["mongodb"] = {
                "status": "placeholder",
                "note": "MongoDB statistics not implemented"
            }
        
        # Get Neo4j statistics (placeholder)
        if self.neo4j:
            stats["database_stats"]["neo4j"] = {
                "status": "placeholder", 
                "note": "Neo4j statistics not implemented"
            }
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all database connections."""
        
        health_status = {
            "database_manager": "healthy",
            "enabled_databases": self.enabled_databases,
            "database_health": {},
            "overall_status": "healthy",
            "timestamp": datetime.now().isoformat()
        }
        
        unhealthy_count = 0
        
        # Check PostgreSQL
        if self.postgresql:
            try:
                pg_health = await self.postgresql.health_check()
                health_status["database_health"]["postgresql"] = pg_health
                if pg_health.get("status") != "healthy":
                    unhealthy_count += 1
            except Exception as e:
                health_status["database_health"]["postgresql"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                unhealthy_count += 1
        
        # Check MongoDB
        if self.mongodb:
            try:
                mongo_health = await self.mongodb.health_check()
                health_status["database_health"]["mongodb"] = mongo_health
                if mongo_health.get("status") != "healthy":
                    unhealthy_count += 1
            except Exception as e:
                health_status["database_health"]["mongodb"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                unhealthy_count += 1
        
        # Check Neo4j
        if self.neo4j:
            try:
                neo4j_health = await self.neo4j.health_check()
                health_status["database_health"]["neo4j"] = neo4j_health
                if neo4j_health.get("status") != "healthy":
                    unhealthy_count += 1
            except Exception as e:
                health_status["database_health"]["neo4j"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                unhealthy_count += 1
        
        # Determine overall status
        if unhealthy_count == 0:
            health_status["overall_status"] = "healthy"
        elif unhealthy_count < len(self.enabled_databases):
            health_status["overall_status"] = "degraded"
        else:
            health_status["overall_status"] = "unhealthy"
            health_status["database_manager"] = "unhealthy"
        
        return health_status
    
    def get_enabled_databases(self) -> List[str]:
        """Get list of enabled database backends."""
        return self.enabled_databases.copy()
    
    def is_database_enabled(self, database: str) -> bool:
        """Check if a specific database backend is enabled."""
        return database in self.enabled_databases
