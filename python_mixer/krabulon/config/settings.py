"""Configuration settings for Krabulon system."""

import os
import yaml
from typing import Dict, List, Optional, Any
from pydantic_settings import BaseSettings
from pydantic import Field
from pathlib import Path


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    # PostgreSQL
    postgresql_host: str = "localhost"
    postgresql_port: int = 5432
    postgresql_database: str = "hvac_crm"
    postgresql_username: str = "postgres"
    postgresql_password: str = "password"
    postgresql_pool_size: int = 10
    postgresql_max_overflow: int = 20
    
    # MongoDB
    mongodb_host: str = Field(default="localhost", env="MONGODB_HOST")
    mongodb_port: int = Field(default=27017, env="MONGODB_PORT")
    mongodb_database: str = Field(default="hvac_equipment", env="MONGODB_DATABASE")
    mongodb_username: str = Field(default="", env="MONGODB_USERNAME")
    mongodb_password: str = Field(default="", env="MONGODB_PASSWORD")
    
    # Neo4j
    neo4j_host: str = Field(default="localhost", env="NEO4J_HOST")
    neo4j_port: int = Field(default=7687, env="NEO4J_PORT")
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_username: str = Field(default="neo4j", env="NEO4J_USERNAME")
    neo4j_password: str = Field(default="password", env="NEO4J_PASSWORD")
    
    # Redis
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_database: int = Field(default=0, env="REDIS_DATABASE")
    redis_password: str = Field(default="", env="REDIS_PASSWORD")
    
    @property
    def postgresql_url(self) -> str:
        """Get PostgreSQL connection URL."""
        return (
            f"postgresql://{self.postgresql_username}:{self.postgresql_password}"
            f"@{self.postgresql_host}:{self.postgresql_port}/{self.postgresql_database}"
        )
    
    @property
    def mongodb_url(self) -> str:
        """Get MongoDB connection URL."""
        if self.mongodb_username and self.mongodb_password:
            return (
                f"mongodb://{self.mongodb_username}:{self.mongodb_password}"
                f"@{self.mongodb_host}:{self.mongodb_port}/{self.mongodb_database}"
            )
        return f"mongodb://{self.mongodb_host}:{self.mongodb_port}/{self.mongodb_database}"

    class Config:
        env_file = ".env"
        extra = "allow"


class AISettings(BaseSettings):
    """AI and LLM configuration settings."""

    openai_api_key: str = Field(default="", env="OPENAI_API_KEY")
    openai_model: str = "gpt-4-turbo-preview"
    openai_temperature: float = 0.1
    openai_max_tokens: int = 4000

    # Local LM Studio integration
    lm_studio_url: str = Field(default="http://*************:1234", env="LM_STUDIO_URL")
    lm_studio_model: str = Field(default="Gemma3-4b", env="LM_STUDIO_MODEL")

    crewai_verbose: bool = True
    crewai_memory: bool = True

    class Config:
        env_file = ".env"
        extra = "allow"


class CrawlingSettings(BaseSettings):
    """Web crawling configuration settings."""
    
    max_pages: int = 50
    delay: float = 1.0
    timeout: int = 30
    user_agent: str = "Krabulon HVAC Bot 1.0"
    respect_robots_txt: bool = True
    
    playwright_headless: bool = True
    playwright_timeout: int = 30000

    class Config:
        env_file = ".env"
        extra = "allow"


class AppSettings(BaseSettings):
    """Main application settings."""

    name: str = "Krabulon HVAC Data Enrichment System"
    version: str = "1.0.0"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000

    # Security
    secret_key: str = Field(default="krabulon-default-secret-key", env="SECRET_KEY")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # Logging
    log_level: str = "INFO"
    log_file: str = "logs/krabulon.log"

    # API
    api_title: str = "Krabulon HVAC API"
    api_description: str = "AI-powered HVAC equipment data enrichment system"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"

    class Config:
        env_file = ".env"
        extra = "allow"


class Settings:
    """Main settings class that loads configuration from YAML and environment."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"
        self._config = self._load_config()
        
        # Initialize sub-settings
        self.app = AppSettings(**self._get_section("app", {}))
        self.database = DatabaseSettings(**self._get_section("databases", {}))
        self.ai = AISettings(**self._get_section("ai", {}))
        self.crawling = CrawlingSettings(**self._get_section("crawling", {}))
        
        # Additional configuration
        self.manufacturers = self._get_section("manufacturers", [])
        self.logging = self._get_section("logging", {})
        self.monitoring = self._get_section("monitoring", {})
        self.tasks = self._get_section("tasks", {})
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        config_file = Path(self.config_path)
        if not config_file.exists():
            return {}
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Expand environment variables
        return self._expand_env_vars(config)
    
    def _expand_env_vars(self, obj: Any) -> Any:
        """Recursively expand environment variables in configuration."""
        if isinstance(obj, dict):
            return {k: self._expand_env_vars(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._expand_env_vars(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)
        return obj
    
    def _get_section(self, section: str, default: Any = None) -> Any:
        """Get a configuration section with flattened keys."""
        section_data = self._config.get(section, default)
        if isinstance(section_data, dict):
            return self._flatten_dict(section_data)
        return section_data
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = "", sep: str = "_") -> Dict[str, Any]:
        """Flatten nested dictionary for pydantic settings."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)


# Global settings instance
settings = Settings()
