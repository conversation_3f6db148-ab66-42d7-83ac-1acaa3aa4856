version: '3.8'

services:
  # Whisper-based STT (CPU only - backup solution)
  whisper-stt:
    image: onerahmet/openai-whisper-asr-webservice:latest
    container_name: hvac-whisper-stt-backup
    environment:
      - ASR_MODEL=large-v2
      - ASR_ENGINE=openai_whisper
    ports:
      - "9000:9000"
    volumes:
      - ./whisper_models:/root/.cache/whisper
    networks:
      - hvac-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for audio processing queue
  redis-audio:
    image: redis:7-alpine
    container_name: hvac-redis-audio-backup
    ports:
      - "6380:6379"
    volumes:
      - redis_audio_data:/data
    networks:
      - hvac-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Local Redis for Krabulon cache
  redis-krabulon:
    image: redis:7-alpine
    container_name: hvac-redis-krabulon
    ports:
      - "6379:6379"
    volumes:
      - redis_krabulon_data:/data
    networks:
      - hvac-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # MongoDB for equipment data (local instance)
  mongodb-local:
    image: mongo:7
    container_name: hvac-mongodb-local
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=hvac-admin-2024
      - MONGO_INITDB_DATABASE=hvac_equipment
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - hvac-network
    restart: unless-stopped

  # Neo4j for equipment relationships (local instance)
  neo4j-local:
    image: neo4j:5
    container_name: hvac-neo4j-local
    environment:
      - NEO4J_AUTH=neo4j/hvac-neo4j-2024
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - hvac-network
    restart: unless-stopped

volumes:
  redis_audio_data:
    driver: local
  redis_krabulon_data:
    driver: local
  mongodb_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local

networks:
  hvac-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
