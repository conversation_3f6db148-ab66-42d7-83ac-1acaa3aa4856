# Copyright (c) "Neo4j"
# Neo4j Sweden AB [https://neo4j.com]
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


class GqlStatusObject:
    """
    Container for GQLSTATUS information.

    **This is a preview**.
    It might be changed without following the deprecation policy.

    See also
    https://github.com/neo4j/neo4j-python-driver/wiki/preview-features

    .. versionadded:: 5.22
    """


class DiagnosticRecord:
    """
    Container additional GQL status information useful for diagnostics.

    The contents are mostly vendor-specific GQL status information.

    **This is a preview**.
    It might be changed without following the deprecation policy.

    See also
    https://github.com/neo4j/neo4j-python-driver/wiki/preview-features

    .. versionadded:: 5.22
    """
