# 🎤 HVAC NVIDIA STT System - Konfiguracja środowiska

# 📧 Konfiguracja email
DOLORES_EMAIL_PASSWORD=hasł*****************************
GRZEGORZ_EMAIL_PASSWORD=hasło_dla_<PERSON><PERSON><PERSON><PERSON>@koldbringers.pl

# 🐘 PostgreSQL
POSTGRES_URL=postgresql://koldbringer:bla<PERSON>ritpol@**************:5432/hvac_crm

# 📦 MinIO
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1

# 🧠 LM Studio (Gemma 3 4B)
LM_STUDIO_URL=http://*************:1234
MODEL_NAME=gemma-3-4b
MAX_TOKENS=2048
TEMPERATURE=0.3

# 🎯 Orchestrator
NVIDIA_STT_URL=http://nvidia-stt-polish:8889
AUDIO_CONVERTER_URL=http://audio-converter:8080
EMAIL_PROCESSOR_URL=http://email-processor:8080
GEMMA_INTEGRATION_URL=http://gemma-integration:8080
GOBACKEND_URL=http://host.docker.internal:8080

# 📡 Redis
REDIS_URL=redis://redis:6379

# 🔧 Audio Converter
INPUT_FORMAT=m4a
OUTPUT_FORMAT=wav
SAMPLE_RATE=16000
CHANNELS=1

# 🎤 NVIDIA STT
NEMO_MODEL_NAME=stt_multilingual_fastconformer_hybrid_large_pc
LANGUAGE=pl
BATCH_SIZE=4
MAX_DURATION=300
