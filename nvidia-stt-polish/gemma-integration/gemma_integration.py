#!/usr/bin/env python3
"""
🧠 Gemma 3 4B Integration Service
Integracja z LM Studio dla analizy transkrypcji w języku polskim
<PERSON>optymalizowany dla HVAC CRM
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import aiohttp
from redis import Redis
import aiofiles

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/gemma_integration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🎯 Modele danych
class TranscriptionAnalysisRequest(BaseModel):
    transcript: str
    source_file: str
    customer_context: Optional[Dict] = None
    hvac_context: bool = True

class TranscriptionAnalysisResponse(BaseModel):
    summary: str
    sentiment: str
    intent: str
    hvac_keywords: List[str]
    customer_insights: Dict
    action_items: List[str]
    priority_score: float
    confidence: float
    processing_time: float

class GemmaIntegrationService:
    """🧠 Serwis integracji z Gemma 3 4B"""
    
    def __init__(self):
        self.app = FastAPI(
            title="🧠 HVAC Gemma Integration",
            description="Analiza transkrypcji z wykorzystaniem Gemma 3 4B",
            version="1.0.0"
        )
        
        # 🔧 Konfiguracja CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 🔧 Konfiguracja LM Studio
        self.lm_studio_url = os.getenv("LM_STUDIO_URL", "http://*************:1234")
        self.model_name = os.getenv("MODEL_NAME", "gemma-3-4b")
        self.max_tokens = int(os.getenv("MAX_TOKENS", "2048"))
        self.temperature = float(os.getenv("TEMPERATURE", "0.3"))
        
        self.redis_client = None
        
        # 🎯 Prompty systemowe
        self.system_prompts = {
            "hvac_analysis": """Jesteś ekspertem HVAC analizującym transkrypcje rozmów z klientami polskiej firmy klimatyzacyjnej Fulmark.
Analizuj transkrypcje pod kątem:
1. Sentyment klienta (pozytywny/neutralny/negatywny)
2. Intencja (zapytanie/reklamacja/zamówienie/serwis)
3. Słowa kluczowe HVAC
4. Insights o kliencie
5. Działania do podjęcia
6. Priorytet (1-10)

Odpowiadaj TYLKO w języku polskim w formacie JSON.""",
            
            "customer_insights": """Analizuj transkrypcję rozmowy z klientem firmy HVAC i wyodrębnij kluczowe informacje:
- Typ klienta (mieszkaniowy/komercyjny)
- Potrzeby klimatyzacyjne
- Budżet (jeśli wspomniany)
- Lokalizacja
- Pilność
- Historia problemów

Odpowiadaj w języku polskim.""",
            
            "technical_analysis": """Analizuj techniczną stronę rozmowy HVAC:
- Typ urządzenia
- Objawy problemów
- Możliwe przyczyny
- Zalecane działania serwisowe
- Części zamienne

Odpowiadaj w języku polskim."""
        }
        
        self._setup_routes()
    
    async def initialize(self):
        """🚀 Inicjalizacja serwisu"""
        logger.info("🚀 Inicjalizacja Gemma Integration Service...")
        
        try:
            # 📡 Redis
            redis_url = os.getenv("REDIS_URL", "redis://redis-krabulon:6379")
            self.redis_client = Redis.from_url(redis_url, decode_responses=True)
            self.redis_client.ping()
            logger.info("✅ Połączenie z Redis nawiązane")
            
            # 🧠 Test połączenia z LM Studio
            await self._test_lm_studio_connection()
            
            # 📁 Katalogi
            self._ensure_directories()
            
            logger.info("✅ Gemma Integration Service zainicjalizowany!")
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji: {e}")
            raise
    
    def _ensure_directories(self):
        """📁 Sprawdzenie katalogów"""
        directories = ['/app/transcriptions', '/app/analysis', '/app/logs']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    async def _test_lm_studio_connection(self):
        """🔍 Test połączenia z LM Studio"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.lm_studio_url}/v1/models") as response:
                    if response.status == 200:
                        models = await response.json()
                        logger.info(f"✅ LM Studio dostępne, modele: {[m.get('id', 'unknown') for m in models.get('data', [])]}")
                    else:
                        logger.warning(f"⚠️ LM Studio odpowiedział kodem: {response.status}")
        except Exception as e:
            logger.error(f"❌ Błąd połączenia z LM Studio: {e}")
            raise
    
    def _setup_routes(self):
        """🛣️ Konfiguracja endpointów"""
        
        @self.app.on_event("startup")
        async def startup_event():
            await self.initialize()
        
        @self.app.get("/health")
        async def health_check():
            """🏥 Status serwisu"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "lm_studio_url": self.lm_studio_url,
                "model_name": self.model_name,
                "connections": {
                    "redis": self.redis_client is not None,
                    "lm_studio": await self._check_lm_studio_health()
                }
            }
        
        @self.app.post("/analyze", response_model=TranscriptionAnalysisResponse)
        async def analyze_transcription(
            request: TranscriptionAnalysisRequest,
            background_tasks: BackgroundTasks
        ):
            """🧠 Analiza transkrypcji"""
            return await self._analyze_transcription(request, background_tasks)
        
        @self.app.post("/analyze/batch")
        async def analyze_batch(
            transcriptions: List[TranscriptionAnalysisRequest],
            background_tasks: BackgroundTasks
        ):
            """📦 Analiza wsadowa"""
            results = []
            for request in transcriptions:
                try:
                    result = await self._analyze_transcription(request, background_tasks)
                    results.append({"source": request.source_file, "result": result, "status": "success"})
                except Exception as e:
                    results.append({"source": request.source_file, "error": str(e), "status": "error"})
            
            return {"results": results}
        
        @self.app.get("/insights/{source_file}")
        async def get_cached_insights(source_file: str):
            """💾 Pobieranie zapisanych analiz"""
            try:
                cache_key = f"analysis:{source_file}"
                cached_data = self.redis_client.get(cache_key)
                
                if cached_data:
                    return json.loads(cached_data)
                else:
                    raise HTTPException(status_code=404, detail="Analiza nie znaleziona")
                    
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Błąd pobierania analizy: {str(e)}")
    
    async def _check_lm_studio_health(self) -> bool:
        """🔍 Sprawdzenie stanu LM Studio"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.lm_studio_url}/v1/models", timeout=5) as response:
                    return response.status == 200
        except:
            return False
    
    async def _analyze_transcription(
        self, 
        request: TranscriptionAnalysisRequest, 
        background_tasks: BackgroundTasks
    ) -> TranscriptionAnalysisResponse:
        """🧠 Główna analiza transkrypcji"""
        
        start_time = time.time()
        
        try:
            # 🔍 Sprawdzenie cache
            cached_result = await self._get_cached_analysis(request.source_file)
            if cached_result:
                logger.info(f"💾 Zwrócono z cache: {request.source_file}")
                return cached_result
            
            # 🧠 Analiza z Gemma
            analysis_results = await self._perform_gemma_analysis(request)
            
            # 📊 Agregacja wyników
            final_result = await self._aggregate_analysis_results(analysis_results, request)
            
            processing_time = time.time() - start_time
            final_result.processing_time = processing_time
            
            # 💾 Zapisanie do cache
            background_tasks.add_task(
                self._cache_analysis_result, 
                request.source_file, 
                final_result
            )
            
            # 💾 Zapisanie do pliku
            background_tasks.add_task(
                self._save_analysis_to_file,
                request.source_file,
                final_result
            )
            
            logger.info(f"✅ Analiza zakończona: {request.source_file} ({processing_time:.2f}s)")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Błąd analizy {request.source_file}: {e}")
            raise HTTPException(status_code=500, detail=f"Błąd analizy: {str(e)}")
    
    async def _get_cached_analysis(self, source_file: str) -> Optional[TranscriptionAnalysisResponse]:
        """💾 Pobieranie z cache"""
        try:
            cache_key = f"analysis:{source_file}"
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                data = json.loads(cached_data)
                return TranscriptionAnalysisResponse(**data)
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ Błąd pobierania z cache: {e}")
            return None
    
    async def _perform_gemma_analysis(self, request: TranscriptionAnalysisRequest) -> Dict:
        """🧠 Wykonanie analizy z Gemma"""
        results = {}
        
        # 🎯 Główna analiza HVAC
        hvac_analysis = await self._call_gemma_api(
            self.system_prompts["hvac_analysis"],
            f"Przeanalizuj tę transkrypcję rozmowy HVAC:\n\n{request.transcript}"
        )
        results["hvac_analysis"] = hvac_analysis
        
        # 👤 Analiza klienta
        customer_analysis = await self._call_gemma_api(
            self.system_prompts["customer_insights"],
            f"Wyodrębnij informacje o kliencie z transkrypcji:\n\n{request.transcript}"
        )
        results["customer_analysis"] = customer_analysis
        
        # 🔧 Analiza techniczna
        if request.hvac_context:
            technical_analysis = await self._call_gemma_api(
                self.system_prompts["technical_analysis"],
                f"Przeanalizuj techniczną stronę rozmowy:\n\n{request.transcript}"
            )
            results["technical_analysis"] = technical_analysis
        
        return results
    
    async def _call_gemma_api(self, system_prompt: str, user_prompt: str) -> Dict:
        """🔗 Wywołanie API Gemma przez LM Studio"""
        try:
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": False
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.lm_studio_url}/v1/chat/completions",
                    json=payload,
                    timeout=120
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        # 🔄 Próba parsowania JSON
                        try:
                            return json.loads(content)
                        except json.JSONDecodeError:
                            return {"raw_response": content}
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ Błąd API Gemma: {response.status} - {error_text}")
                        return {"error": f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"❌ Błąd wywołania Gemma API: {e}")
            return {"error": str(e)}
    
    async def _aggregate_analysis_results(
        self, 
        analysis_results: Dict, 
        request: TranscriptionAnalysisRequest
    ) -> TranscriptionAnalysisResponse:
        """📊 Agregacja wyników analizy"""
        
        # 🎯 Wyodrębnienie głównych wyników
        hvac_data = analysis_results.get("hvac_analysis", {})
        customer_data = analysis_results.get("customer_analysis", {})
        technical_data = analysis_results.get("technical_analysis", {})
        
        # 📝 Tworzenie podsumowania
        summary_parts = []
        if isinstance(hvac_data, dict) and "summary" in hvac_data:
            summary_parts.append(hvac_data["summary"])
        if isinstance(customer_data, dict) and "raw_response" in customer_data:
            summary_parts.append(customer_data["raw_response"][:200] + "...")
        
        summary = " | ".join(summary_parts) if summary_parts else "Analiza wykonana"
        
        # 🎭 Sentiment
        sentiment = hvac_data.get("sentiment", "neutralny") if isinstance(hvac_data, dict) else "neutralny"
        
        # 🎯 Intent
        intent = hvac_data.get("intent", "zapytanie") if isinstance(hvac_data, dict) else "zapytanie"
        
        # 🔑 Słowa kluczowe HVAC
        hvac_keywords = hvac_data.get("hvac_keywords", []) if isinstance(hvac_data, dict) else []
        if not hvac_keywords:
            hvac_keywords = self._extract_basic_hvac_keywords(request.transcript)
        
        # 👤 Insights o kliencie
        customer_insights = customer_data if isinstance(customer_data, dict) else {}
        
        # ✅ Działania do podjęcia
        action_items = hvac_data.get("action_items", []) if isinstance(hvac_data, dict) else []
        
        # 📊 Priorytet
        priority_score = float(hvac_data.get("priority_score", 5.0)) if isinstance(hvac_data, dict) else 5.0
        
        # 🎯 Confidence
        confidence = self._calculate_confidence(analysis_results)
        
        return TranscriptionAnalysisResponse(
            summary=summary,
            sentiment=sentiment,
            intent=intent,
            hvac_keywords=hvac_keywords,
            customer_insights=customer_insights,
            action_items=action_items,
            priority_score=priority_score,
            confidence=confidence,
            processing_time=0.0  # Będzie ustawione później
        )
    
    def _extract_basic_hvac_keywords(self, transcript: str) -> List[str]:
        """🔑 Podstawowe wyodrębnienie słów kluczowych"""
        keywords = [
            "klimatyzacja", "klimatyzator", "pompa ciepła", "wentylacja",
            "chłodzenie", "ogrzewanie", "serwis", "naprawa", "instalacja",
            "LG", "Daikin", "Samsung", "Mitsubishi", "split", "multi split"
        ]
        
        found_keywords = []
        transcript_lower = transcript.lower()
        
        for keyword in keywords:
            if keyword.lower() in transcript_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _calculate_confidence(self, analysis_results: Dict) -> float:
        """🎯 Obliczenie confidence score"""
        confidence = 0.5  # Bazowa wartość
        
        # Sprawdzenie jakości odpowiedzi
        for key, result in analysis_results.items():
            if isinstance(result, dict):
                if "error" not in result:
                    confidence += 0.15
                if len(str(result)) > 100:  # Szczegółowa odpowiedź
                    confidence += 0.1
        
        return min(0.95, max(0.3, confidence))
    
    async def _cache_analysis_result(self, source_file: str, result: TranscriptionAnalysisResponse):
        """💾 Zapisanie do cache"""
        try:
            cache_key = f"analysis:{source_file}"
            cache_data = result.dict()
            
            self.redis_client.setex(
                cache_key,
                7200,  # 2 godziny TTL
                json.dumps(cache_data, ensure_ascii=False)
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Błąd zapisywania do cache: {e}")
    
    async def _save_analysis_to_file(self, source_file: str, result: TranscriptionAnalysisResponse):
        """💾 Zapisanie analizy do pliku"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"/app/analysis/{timestamp}_{source_file}_analysis.json"
            
            async with aiofiles.open(filename, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(result.dict(), ensure_ascii=False, indent=2))
                
            logger.info(f"💾 Analiza zapisana: {filename}")
            
        except Exception as e:
            logger.warning(f"⚠️ Błąd zapisywania analizy: {e}")

# 🚀 Uruchomienie serwisu
if __name__ == "__main__":
    service = GemmaIntegrationService()
    
    uvicorn.run(
        service.app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
