# 🚀 HVAC NVIDIA STT - Quick Start Guide

## ⚡ Szybkie Uruchomienie (5 minut)

### 1. 🔧 Przygotowanie środowiska

```bash
# Klonowanie/przejście do katalogu
cd nvidia-stt-polish

# Kopiowanie konfiguracji
cp .env.example .env

# <PERSON><PERSON><PERSON><PERSON> haseł email (WYMAGANE!)
nano .env
```

**<PERSON>taw hasła email:**
```bash
DOLORES_EMAIL_PASSWORD=twoje_hasło_dolores
GRZEGORZ_EMAIL_PASSWORD=twoje_hasło_grzegorz
```

### 2. 🚀 Uruchomienie systemu

```bash
# Nadanie uprawnień
chmod +x start_system.sh test_system.sh

# Uruchomienie (automatyczne)
./start_system.sh
```

### 3. 🧪 Test systemu

```bash
# Test wszystkich komponentów
./test_system.sh
```

### 4. ✅ Sprawdzenie działania

```bash
# Dashboard transkrypcji
curl http://localhost:8080/api/transcription/dashboard

# Test uploadu pliku
curl -X POST \
  -F "audio_file=@test.m4a" \
  -F "email_source=test" \
  http://localhost:8080/api/transcription/upload
```

---

## 🎯 Główne Endpointy

| Serwis | URL | Opis |
|--------|-----|------|
| 🎯 **Orchestrator** | `http://localhost:9000` | Główny koordynator |
| 🎤 **NVIDIA STT** | `http://localhost:8888` | Transkrypcja polska |
| 🔄 **Audio Converter** | `http://localhost:8081` | Konwersja M4A→WAV |
| 📧 **Email Processor** | `http://localhost:8082` | Monitoring emaili |
| 🧠 **Gemma Integration** | `http://localhost:8083` | Analiza AI |
| 🏠 **Go Backend** | `http://localhost:8080` | HVAC CRM API |

---

## 📧 Automatyczne Przetwarzanie Emaili

System automatycznie:
- ✅ Monitoruje emaile co 5 minut
- ✅ Pobiera załączniki M4A
- ✅ Konwertuje do WAV
- ✅ Transkrybuje w języku polskim
- ✅ Analizuje z AI (Gemma 3 4B)
- ✅ Zapisuje do bazy danych

**Konta monitorowane:**
- `<EMAIL>` - Transkrypcje
- `<EMAIL>` - Emaile klientów

---

## 🎤 Ręczna Transkrypcja

### Upload przez interfejs web:
```
http://localhost:8080/api/transcription/upload
```

### Upload przez curl:
```bash
curl -X POST \
  -F "audio_file=@nagranie.m4a" \
  -F "email_source=manual" \
  http://localhost:8080/api/transcription/upload
```

---

## 📊 Monitoring i Logi

```bash
# Status wszystkich serwisów
docker-compose ps

# Logi w czasie rzeczywistym
docker-compose logs -f

# Logi konkretnego serwisu
docker-compose logs -f transcription-orchestrator

# Statystyki
curl http://localhost:9000/stats
```

---

## 🔧 Zarządzanie Systemem

```bash
# ⏸️ Zatrzymanie
docker-compose down

# 🚀 Uruchomienie
docker-compose up -d

# 🔄 Restart
docker-compose restart

# 🧹 Czyszczenie (UWAGA: usuwa dane!)
docker-compose down -v
```

---

## 🐛 Rozwiązywanie Problemów

### Problem: NVIDIA GPU niedostępne
```bash
# Test NVIDIA Docker
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

### Problem: LM Studio niedostępne
```bash
# Test LM Studio
curl http://*************:1234/v1/models
```

### Problem: Błędy email
```bash
# Sprawdź hasła w .env
cat .env | grep EMAIL_PASSWORD

# Sprawdź logi email processor
docker-compose logs email-processor
```

### Problem: Brak transkrypcji
```bash
# Test NVIDIA STT
curl http://localhost:8888/health

# Test modeli
curl http://localhost:8888/models
```

---

## 📈 Wydajność

**Typowe czasy:**
- Konwersja M4A→WAV: 2-5s
- NVIDIA STT: 10-30s (z GPU)
- Gemma analiza: 5-15s
- **Całkowity pipeline: 20-60s**

**Wymagania:**
- NVIDIA GPU (zalecane)
- 16GB RAM minimum
- SSD dla szybkiego I/O

---

## 🎉 Gotowe!

System jest teraz gotowy do:
- 📧 Automatycznego przetwarzania emaili z M4A
- 🎤 Transkrypcji w języku polskim
- 🧠 Analizy AI z kontekstem HVAC
- 📊 Integracji z CRM

**Sprawdź dashboard:** http://localhost:8080/api/transcription/dashboard

**WebSocket real-time:** ws://localhost:9000/ws

---

## 📚 Więcej Informacji

- 📖 **Pełna dokumentacja:** `README.md`
- 🔧 **Przykłady API:** `API_EXAMPLES.md`
- 🧪 **Testy:** `./test_system.sh`
- 🐛 **Logi:** `./logs/`

**Powodzenia z transkrypcją HVAC!** 🎤🏠❄️
