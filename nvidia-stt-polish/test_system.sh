#!/bin/bash

# 🧪 HVAC NVIDIA STT System Test Script
# Testowanie wszystkich komponentów systemu transkrypcji

echo "🧪 =========================================="
echo "🧪 HVAC NVIDIA STT SYSTEM TESTS"
echo "🧪 =========================================="

# 🔧 Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 📊 Funkcje pomocnicze
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    echo -n "🔍 Testing $name... "
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL (HTTP $response)${NC}"
        return 1
    fi
}

test_health() {
    local name="$1"
    local url="$2"
    
    echo -n "🏥 Health check $name... "
    
    response=$(curl -s "$url/health" 2>/dev/null)
    
    if echo "$response" | grep -q "healthy\|status.*active"; then
        echo -e "${GREEN}✅ HEALTHY${NC}"
        return 0
    else
        echo -e "${RED}❌ UNHEALTHY${NC}"
        echo "   Response: $response"
        return 1
    fi
}

# 🚀 Sprawdzenie czy system jest uruchomiony
echo "🔍 Sprawdzanie czy system jest uruchomiony..."

if ! docker-compose ps | grep -q "Up"; then
    echo -e "${RED}❌ System nie jest uruchomiony!${NC}"
    echo "Uruchom system: ./start_system.sh"
    exit 1
fi

echo -e "${GREEN}✅ System uruchomiony${NC}"
echo ""

# 🧪 Testy podstawowych endpointów
echo "🧪 Testowanie podstawowych endpointów..."

# Orchestrator
test_health "Orchestrator" "http://localhost:9000"
test_endpoint "Orchestrator Stats" "http://localhost:9000/stats"

# Audio Converter
test_health "Audio Converter" "http://localhost:8081"
test_endpoint "Audio Formats" "http://localhost:8081/formats"

# Email Processor
test_health "Email Processor" "http://localhost:8082"
test_endpoint "Email Stats" "http://localhost:8082/stats"

# Gemma Integration
test_health "Gemma Integration" "http://localhost:8083"

# NVIDIA STT
test_health "NVIDIA STT" "http://localhost:8888"
test_endpoint "STT Models" "http://localhost:8888/models"

# Redis
echo -n "📊 Testing Redis... "
if redis-cli -h localhost -p 6379 ping 2>/dev/null | grep -q "PONG"; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
fi

echo ""

# 🧪 Test integracji z Go Backend
echo "🧪 Testowanie integracji z Go Backend..."

test_endpoint "Go Backend Health" "http://localhost:8080/health"
test_endpoint "Transcription Dashboard" "http://localhost:8080/api/transcription/dashboard"
test_endpoint "Transcription Health" "http://localhost:8080/api/transcription/health"

echo ""

# 🧪 Test LM Studio
echo "🧪 Testowanie LM Studio (Gemma 3 4B)..."

echo -n "🧠 Testing LM Studio... "
if curl -s "http://*************:1234/v1/models" 2>/dev/null | grep -q "gemma"; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${YELLOW}⚠️ LM Studio niedostępne${NC}"
fi

echo ""

# 🧪 Test funkcjonalny - upload pliku testowego
echo "🧪 Test funkcjonalny - transkrypcja..."

# Sprawdzenie czy istnieje plik testowy
if [ ! -f "test_audio.wav" ]; then
    echo "📁 Tworzenie pliku testowego audio..."
    # Generowanie prostego pliku WAV (1 sekunda ciszy)
    ffmpeg -f lavfi -i "anullsrc=channel_layout=mono:sample_rate=16000" -t 1 test_audio.wav -y 2>/dev/null
fi

if [ -f "test_audio.wav" ]; then
    echo -n "🎤 Testing transcription upload... "
    
    response=$(curl -s -X POST \
        -F "audio_file=@test_audio.wav" \
        -F "email_source=test" \
        "http://localhost:8080/api/transcription/upload" 2>/dev/null)
    
    if echo "$response" | grep -q "success\|job_id"; then
        echo -e "${GREEN}✅ PASS${NC}"
        
        # Wyodrębnienie job_id jeśli dostępne
        job_id=$(echo "$response" | grep -o '"job_id":"[^"]*"' | cut -d'"' -f4)
        if [ ! -z "$job_id" ]; then
            echo "   Job ID: $job_id"
            
            # Test sprawdzenia statusu
            echo -n "📊 Testing job status... "
            status_response=$(curl -s "http://localhost:8080/api/transcription/status/$job_id" 2>/dev/null)
            
            if echo "$status_response" | grep -q "job_id\|status"; then
                echo -e "${GREEN}✅ PASS${NC}"
            else
                echo -e "${RED}❌ FAIL${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ FAIL${NC}"
        echo "   Response: $response"
    fi
else
    echo -e "${YELLOW}⚠️ Brak pliku testowego${NC}"
fi

echo ""

# 🧪 Test email check
echo "🧪 Test sprawdzenia emaili..."

echo -n "📧 Testing email check trigger... "
response=$(curl -s -X POST "http://localhost:9000/trigger/email-check" 2>/dev/null)

if echo "$response" | grep -q "success\|triggered"; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
    echo "   Response: $response"
fi

echo ""

# 📊 Podsumowanie
echo "📊 =========================================="
echo "📊 PODSUMOWANIE TESTÓW"
echo "📊 =========================================="

# Sprawdzenie logów na błędy
echo "🔍 Sprawdzanie logów na błędy..."

error_count=0

if docker-compose logs --tail=50 2>/dev/null | grep -i "error\|fail\|exception" | grep -v "test" >/dev/null; then
    echo -e "${YELLOW}⚠️ Znaleziono błędy w logach${NC}"
    error_count=$((error_count + 1))
else
    echo -e "${GREEN}✅ Brak błędów w logach${NC}"
fi

# Sprawdzenie wykorzystania zasobów
echo "💻 Sprawdzanie wykorzystania zasobów..."

if command -v docker stats >/dev/null 2>&1; then
    echo "📊 Wykorzystanie CPU i RAM (5 sekund):"
    timeout 5 docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" 2>/dev/null || echo "   Nie udało się pobrać statystyk"
fi

echo ""

# 🎯 Końcowe podsumowanie
if [ $error_count -eq 0 ]; then
    echo -e "${GREEN}🎉 WSZYSTKIE TESTY PRZESZŁY POMYŚLNIE!${NC}"
    echo "✅ System transkrypcji działa poprawnie"
    echo "🎤 Gotowy do przetwarzania plików M4A"
    echo "📧 Monitoring emaili aktywny"
    echo "🧠 Analiza AI dostępna"
else
    echo -e "${YELLOW}⚠️ SYSTEM DZIAŁA Z OSTRZEŻENIAMI${NC}"
    echo "Sprawdź logi: docker-compose logs -f"
fi

echo ""
echo "📖 Więcej informacji: README.md"
echo "🐛 Problemy: sprawdź logi w ./logs/"
echo "🔧 Konfiguracja: .env"

# Czyszczenie pliku testowego
if [ -f "test_audio.wav" ]; then
    rm -f test_audio.wav
fi
