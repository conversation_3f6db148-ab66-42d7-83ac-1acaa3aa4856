2025-05-30 14:30:28,908 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:30:38,204 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:30:40,482 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:30:48,487 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:30:51,388 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:30:59,393 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:02,369 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:11,674 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:14,005 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:22,010 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:24,677 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:32,682 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:38,261 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:46,266 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:47,870 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:31:55,874 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:31:57,480 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:05,483 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:07,126 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:16,432 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:18,081 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:26,085 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:27,787 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:35,792 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:38,334 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:47,641 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:32:51,735 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:32:59,739 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:33:07,026 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:33:15,030 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:33:29,998 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:33:38,002 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:34:05,749 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:34:13,753 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:35:08,443 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:35:16,448 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:36:19,917 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:36:27,921 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:37:31,588 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:37:39,593 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:38:43,442 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:38:51,446 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:39:54,997 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:40:04,325 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:06,551 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:15,887 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:17,803 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:25,807 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:27,496 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:35,501 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:37,126 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:46,467 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:48,235 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:41:56,240 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:41:57,997 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:42:06,001 - __main__ - ERROR - ❌ Błąd inicjalizacji: Error -3 connecting to redis:6379. Temporary failure in name resolution.
2025-05-30 14:42:47,271 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:45:04,162 - __main__ - ERROR - ❌ Błąd inicjalizacji: Timeout connecting to server
2025-05-30 14:51:05,243 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:51:05,247 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 14:51:05,493 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 14:51:05,494 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 14:51:14,820 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:22,823 - __main__ - WARNING - ⚠️ audio_converter: http://audio-converter:8080 - Cannot connect to host audio-converter:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:30,826 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:38,830 - __main__ - WARNING - ⚠️ gemma_integration: http://gemma-integration:8080 - Cannot connect to host gemma-integration:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:51:50,296 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 14:51:50,297 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 14:51:50,297 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 14:51:50,297 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 14:51:58,300 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:53:08,952 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:54:19,606 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:54:56,596 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:54:56,599 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 14:54:56,850 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 14:54:56,851 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 14:54:56,853 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Connection refused]
2025-05-30 14:54:56,855 - __main__ - WARNING - ⚠️ audio_converter: http://audio-converter:8080 - Cannot connect to host audio-converter:8080 ssl:default [Connection refused]
2025-05-30 14:54:56,856 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:54:56,858 - __main__ - WARNING - ⚠️ gemma_integration: http://gemma-integration:8080 - Cannot connect to host gemma-integration:8080 ssl:default [Connection refused]
2025-05-30 14:55:08,249 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 14:55:08,249 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 14:55:08,249 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 14:55:08,249 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 14:55:08,292 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:56:09,630 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:57:12,393 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:58:23,012 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 14:59:29,757 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 14:59:29,760 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 14:59:30,000 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 14:59:30,000 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 14:59:30,002 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Connection refused]
2025-05-30 14:59:30,334 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 14:59:30,336 - __main__ - WARNING - ⚠️ email_processor: http://email-processor:8080 - Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 14:59:30,342 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 14:59:41,995 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 14:59:41,995 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 14:59:41,995 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 14:59:41,995 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 15:01:57,381 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection timed out]
2025-05-30 15:03:08,117 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:04:18,799 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:05:29,385 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:06:40,109 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:07:50,901 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:08:57,757 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Connection refused]
2025-05-30 15:10:08,514 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:11:19,165 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:12:29,515 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:13:38,832 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:14:48,550 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:15:58,194 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:17:08,724 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:18:18,733 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:19:28,588 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:20:38,442 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:21:48,568 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:22:58,530 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:24:08,582 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:25:18,753 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:26:29,044 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:27:39,459 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:28:51,150 - __main__ - ERROR - ❌ Błąd w email monitoring: Cannot connect to host email-processor:8080 ssl:default [Temporary failure in name resolution]
2025-05-30 15:30:04,926 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:32:31,516 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748619151_0
2025-05-30 15:32:31,516 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: 'test_audio.wav'
2025-05-30 15:32:31,517 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748619151_0: [Errno 2] No such file or directory: 'test_audio.wav'
2025-05-30 15:33:54,336 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748619234_0
2025-05-30 15:33:54,341 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: '/home/<USER>/HVAC/unifikacja/nvidia-stt-polish/audio_input/test_audio.wav'
2025-05-30 15:33:54,341 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748619234_0: [Errno 2] No such file or directory: '/home/<USER>/HVAC/unifikacja/nvidia-stt-polish/audio_input/test_audio.wav'
2025-05-30 15:35:27,218 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:40:42,444 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:45:09,019 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 15:45:09,023 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 15:45:09,259 - __main__ - WARNING - ⚠️ PostgreSQL niedostępny: password authentication failed for user "koldbringer"
2025-05-30 15:45:09,260 - __main__ - INFO - 🔄 Kontynuacja bez PostgreSQL...
2025-05-30 15:45:17,265 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:45:17,341 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 15:45:17,345 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 15:45:17,353 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 15:45:27,828 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 15:45:27,828 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 15:45:27,828 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 15:45:27,828 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 15:45:36,874 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:45:41,749 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748619941_0
2025-05-30 15:45:41,749 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 15:45:41,749 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748619941_0: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 15:46:40,250 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 15:46:40,254 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 15:46:41,919 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 15:46:41,966 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 15:46:49,970 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:46:50,031 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 15:46:50,034 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 15:46:50,039 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 15:47:00,799 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 15:47:00,800 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 15:47:00,800 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 15:47:00,800 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 15:47:07,357 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748620027_0
2025-05-30 15:47:08,938 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:47:16,855 - __main__ - ERROR - ❌ Błąd transkrypcji: Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:47:16,855 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748620027_0: Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 15:52:26,544 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}, {'account': 'customer', 'result': {'error': "b'[AUTHENTICATIONFAILED] Authentication failed.'"}}]}
2025-05-30 15:57:39,097 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:02:53,184 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:06:32,969 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 16:06:32,972 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 16:06:33,732 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 16:06:33,774 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 16:06:41,778 - __main__ - WARNING - ⚠️ nvidia_stt: http://nvidia-stt-polish:8889 - Cannot connect to host nvidia-stt-polish:8889 ssl:default [Temporary failure in name resolution]
2025-05-30 16:06:41,851 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 16:06:41,854 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 16:06:41,863 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 16:06:52,178 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 16:06:52,179 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 16:06:52,179 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 16:06:52,179 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 16:06:52,937 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:08:59,037 - __main__ - INFO - 🚀 Inicjalizacja Transcription Orchestrator...
2025-05-30 16:08:59,040 - __main__ - INFO - ✅ Połączenie z Redis nawiązane
2025-05-30 16:08:59,792 - __main__ - INFO - ✅ Schema bazy danych sprawdzona
2025-05-30 16:08:59,833 - __main__ - INFO - ✅ Połączenie z PostgreSQL nawiązane
2025-05-30 16:08:59,841 - __main__ - INFO - ✅ nvidia_stt: http://simple-stt:8889
2025-05-30 16:08:59,911 - __main__ - INFO - ✅ audio_converter: http://audio-converter:8080
2025-05-30 16:08:59,914 - __main__ - INFO - ✅ email_processor: http://email-processor:8080
2025-05-30 16:08:59,920 - __main__ - INFO - ✅ gemma_integration: http://gemma-integration:8080
2025-05-30 16:09:10,623 - __main__ - WARNING - ⚠️ gobackend: http://host.docker.internal:8080 - 
2025-05-30 16:09:10,623 - __main__ - INFO - ✅ Transcription Orchestrator zainicjalizowany!
2025-05-30 16:09:10,623 - __main__ - INFO - 🔄 Uruchomienie job processor loop...
2025-05-30 16:09:10,623 - __main__ - INFO - 📧 Uruchomienie email monitoring loop...
2025-05-30 16:09:11,356 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:10:13,700 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748621413_0
2025-05-30 16:10:14,126 - __main__ - INFO - ✅ Zadanie zakończone: job_1748621413_0 (0.18s)
2025-05-30 16:12:37,053 - __main__ - INFO - 🎤 Rozpoczęcie przetwarzania: job_1748621557_0
2025-05-30 16:12:37,142 - __main__ - ERROR - ❌ Błąd transkrypcji: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 16:12:37,142 - __main__ - ERROR - ❌ Błąd przetwarzania job_1748621557_0: [Errno 2] No such file or directory: '/app/audio_input/test_audio.wav'
2025-05-30 16:14:26,511 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:19:40,260 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:24:53,959 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.***************}}]}
2025-05-30 16:30:09,181 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:35:23,240 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:40:37,807 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.****************}}]}
2025-05-30 16:45:53,694 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
2025-05-30 16:51:08,052 - __main__ - INFO - 📧 Email check result: {'processed_accounts': [{'account': 'transcription', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}, {'account': 'customer', 'result': {'processed_emails': 0, 'attachments_found': 0, 'processing_time': 0.*****************}}]}
