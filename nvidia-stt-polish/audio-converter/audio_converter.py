#!/usr/bin/env python3
"""
🔄 Audio Converter Service
Konwersja plików M4A z emaili do formatu WAV dla NVIDIA STT
Zoptymalizowany dla polskich nagrań HVAC
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from pydub import AudioSegment
import librosa
import soundfile as sf
import numpy as np
from redis import Redis
import aiofiles

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/audio_converter.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🎯 Modele danych
class ConversionRequest(BaseModel):
    input_file_path: str
    output_format: str = "wav"
    sample_rate: int = 16000
    channels: int = 1
    normalize: bool = True

class ConversionResponse(BaseModel):
    output_file_path: str
    original_format: str
    target_format: str
    original_duration: float
    converted_duration: float
    file_size_mb: float
    processing_time: float
    quality_metrics: Dict

class AudioConverterService:
    """🔄 Serwis konwersji audio M4A -> WAV"""
    
    def __init__(self):
        self.app = FastAPI(
            title="🔄 HVAC Audio Converter",
            description="Konwersja plików audio M4A do WAV dla NVIDIA STT",
            version="1.0.0"
        )
        
        # 🔧 Konfiguracja CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.redis_client = None
        self.supported_formats = {
            'input': ['m4a', 'mp3', 'wav', 'flac', 'aac', 'ogg'],
            'output': ['wav', 'mp3', 'flac']
        }
        
        self._setup_routes()
    
    async def initialize(self):
        """🚀 Inicjalizacja serwisu"""
        logger.info("🚀 Inicjalizacja Audio Converter Service...")
        
        try:
            # 📡 Połączenie z Redis
            redis_url = os.getenv("REDIS_URL", "redis://redis-krabulon:6379")
            self.redis_client = Redis.from_url(redis_url, decode_responses=True)
            self.redis_client.ping()
            logger.info("✅ Połączenie z Redis nawiązane")
            
            # 📁 Sprawdzenie katalogów
            self._ensure_directories()
            
            logger.info("✅ Audio Converter Service zainicjalizowany!")
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji: {e}")
            raise
    
    def _ensure_directories(self):
        """📁 Sprawdzenie i utworzenie katalogów"""
        directories = ['/app/input', '/app/output', '/app/temp', '/app/logs']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _setup_routes(self):
        """🛣️ Konfiguracja endpointów API"""
        
        @self.app.on_event("startup")
        async def startup_event():
            await self.initialize()
        
        @self.app.get("/health")
        async def health_check():
            """🏥 Sprawdzenie stanu serwisu"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "supported_input_formats": self.supported_formats['input'],
                "supported_output_formats": self.supported_formats['output'],
                "ffmpeg_available": self._check_ffmpeg()
            }
        
        @self.app.post("/convert", response_model=ConversionResponse)
        async def convert_audio(
            background_tasks: BackgroundTasks,
            audio_file: UploadFile = File(...),
            output_format: str = "wav",
            sample_rate: int = 16000,
            channels: int = 1,
            normalize: bool = True
        ):
            """🔄 Konwersja pliku audio"""
            return await self._convert_audio_file(
                audio_file, output_format, sample_rate, channels, normalize, background_tasks
            )
        
        @self.app.post("/convert/batch")
        async def convert_batch(
            background_tasks: BackgroundTasks,
            files: List[UploadFile] = File(...),
            output_format: str = "wav",
            sample_rate: int = 16000
        ):
            """📦 Konwersja wsadowa"""
            results = []
            for file in files:
                try:
                    result = await self._convert_audio_file(
                        file, output_format, sample_rate, 1, True, background_tasks
                    )
                    results.append({"file": file.filename, "result": result, "status": "success"})
                except Exception as e:
                    results.append({"file": file.filename, "error": str(e), "status": "error"})
            
            return {"results": results}
        
        @self.app.get("/formats")
        async def get_supported_formats():
            """📋 Lista obsługiwanych formatów"""
            return self.supported_formats
    
    async def _convert_audio_file(
        self,
        audio_file: UploadFile,
        output_format: str,
        sample_rate: int,
        channels: int,
        normalize: bool,
        background_tasks: BackgroundTasks
    ) -> ConversionResponse:
        """🔄 Konwersja pojedynczego pliku"""
        
        start_time = time.time()
        
        try:
            # 📝 Walidacja formatu
            input_format = audio_file.filename.split('.')[-1].lower()
            if input_format not in self.supported_formats['input']:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Nieobsługiwany format wejściowy: {input_format}"
                )
            
            if output_format not in self.supported_formats['output']:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Nieobsługiwany format wyjściowy: {output_format}"
                )
            
            # 💾 Zapisanie pliku wejściowego
            input_path = f"/app/input/{audio_file.filename}"
            async with aiofiles.open(input_path, 'wb') as f:
                content = await audio_file.read()
                await f.write(content)
            
            # 📊 Analiza pliku oryginalnego
            original_stats = await self._analyze_audio_file(input_path)
            
            # 🔄 Konwersja
            output_filename = f"{Path(audio_file.filename).stem}.{output_format}"
            output_path = f"/app/output/{output_filename}"
            
            converted_stats = await self._perform_conversion(
                input_path, output_path, output_format, sample_rate, channels, normalize
            )
            
            # 📊 Metryki jakości
            quality_metrics = self._calculate_quality_metrics(original_stats, converted_stats)
            
            processing_time = time.time() - start_time
            
            # 💾 Cache wyników
            await self._cache_conversion_result(audio_file.filename, output_filename, quality_metrics)
            
            # 🗑️ Czyszczenie w tle
            background_tasks.add_task(self._cleanup_files, [input_path])
            
            logger.info(f"✅ Konwersja zakończona: {audio_file.filename} -> {output_filename} ({processing_time:.2f}s)")
            
            return ConversionResponse(
                output_file_path=output_path,
                original_format=input_format,
                target_format=output_format,
                original_duration=original_stats['duration'],
                converted_duration=converted_stats['duration'],
                file_size_mb=round(os.path.getsize(output_path) / (1024*1024), 2),
                processing_time=processing_time,
                quality_metrics=quality_metrics
            )
            
        except Exception as e:
            logger.error(f"❌ Błąd konwersji {audio_file.filename}: {e}")
            raise HTTPException(status_code=500, detail=f"Błąd konwersji: {str(e)}")
    
    async def _analyze_audio_file(self, file_path: str) -> Dict:
        """📊 Analiza pliku audio"""
        try:
            # Użycie pydub do podstawowej analizy
            audio = AudioSegment.from_file(file_path)
            
            # Użycie librosa do szczegółowej analizy
            y, sr = librosa.load(file_path, sr=None)
            
            return {
                "duration": len(audio) / 1000.0,  # w sekundach
                "sample_rate": sr,
                "channels": audio.channels,
                "frame_rate": audio.frame_rate,
                "sample_width": audio.sample_width,
                "rms_energy": float(np.sqrt(np.mean(y**2))),
                "zero_crossing_rate": float(np.mean(librosa.feature.zero_crossing_rate(y))),
                "spectral_centroid": float(np.mean(librosa.feature.spectral_centroid(y=y, sr=sr)))
            }
            
        except Exception as e:
            logger.error(f"❌ Błąd analizy audio: {e}")
            return {"duration": 0, "sample_rate": 0, "channels": 0}
    
    async def _perform_conversion(
        self, 
        input_path: str, 
        output_path: str, 
        output_format: str, 
        sample_rate: int, 
        channels: int, 
        normalize: bool
    ) -> Dict:
        """🔄 Wykonanie konwersji"""
        try:
            # Ładowanie audio z pydub
            audio = AudioSegment.from_file(input_path)
            
            # Konwersja do mono jeśli wymagane
            if channels == 1 and audio.channels > 1:
                audio = audio.set_channels(1)
            
            # Zmiana sample rate
            if sample_rate != audio.frame_rate:
                audio = audio.set_frame_rate(sample_rate)
            
            # Normalizacja
            if normalize:
                audio = audio.normalize()
            
            # Eksport do docelowego formatu
            if output_format == "wav":
                audio.export(output_path, format="wav", parameters=["-acodec", "pcm_s16le"])
            elif output_format == "mp3":
                audio.export(output_path, format="mp3", bitrate="128k")
            elif output_format == "flac":
                audio.export(output_path, format="flac")
            
            # Analiza pliku wynikowego
            return await self._analyze_audio_file(output_path)
            
        except Exception as e:
            logger.error(f"❌ Błąd konwersji: {e}")
            raise
    
    def _calculate_quality_metrics(self, original: Dict, converted: Dict) -> Dict:
        """📊 Obliczenie metryki jakości"""
        try:
            return {
                "duration_preserved": abs(original['duration'] - converted['duration']) < 0.1,
                "sample_rate_ratio": converted['sample_rate'] / original['sample_rate'] if original['sample_rate'] > 0 else 1.0,
                "energy_preservation": converted.get('rms_energy', 0) / original.get('rms_energy', 1) if original.get('rms_energy', 0) > 0 else 1.0,
                "quality_score": self._calculate_overall_quality_score(original, converted)
            }
        except Exception as e:
            logger.warning(f"⚠️ Błąd obliczania metryki: {e}")
            return {"quality_score": 0.8}  # Domyślna wartość
    
    def _calculate_overall_quality_score(self, original: Dict, converted: Dict) -> float:
        """📊 Ogólna ocena jakości konwersji"""
        score = 1.0
        
        # Sprawdzenie zachowania czasu trwania
        duration_diff = abs(original.get('duration', 0) - converted.get('duration', 0))
        if duration_diff > 0.5:  # Więcej niż 0.5s różnicy
            score -= 0.2
        
        # Sprawdzenie sample rate
        if converted.get('sample_rate', 0) < 16000:
            score -= 0.1
        
        # Sprawdzenie energii sygnału
        original_energy = original.get('rms_energy', 0)
        converted_energy = converted.get('rms_energy', 0)
        if original_energy > 0:
            energy_ratio = converted_energy / original_energy
            if energy_ratio < 0.5 or energy_ratio > 2.0:
                score -= 0.1
        
        return max(0.0, min(1.0, score))
    
    def _check_ffmpeg(self) -> bool:
        """🔍 Sprawdzenie dostępności FFmpeg"""
        try:
            import subprocess
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    async def _cache_conversion_result(self, input_filename: str, output_filename: str, metrics: Dict):
        """💾 Zapisanie wyniku do cache"""
        try:
            cache_key = f"conversion:{input_filename}"
            cache_data = {
                "output_filename": output_filename,
                "metrics": metrics,
                "timestamp": datetime.now().isoformat()
            }
            
            self.redis_client.setex(
                cache_key,
                1800,  # 30 minut TTL
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Nie udało się zapisać do cache: {e}")
    
    async def _cleanup_files(self, file_paths: List[str]):
        """🗑️ Usunięcie plików tymczasowych"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"🗑️ Usunięto plik: {file_path}")
            except Exception as e:
                logger.warning(f"⚠️ Nie udało się usunąć pliku {file_path}: {e}")

# 🚀 Uruchomienie serwisu
if __name__ == "__main__":
    service = AudioConverterService()
    
    uvicorn.run(
        service.app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
