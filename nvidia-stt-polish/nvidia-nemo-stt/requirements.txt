# 🎤 NVIDIA NeMo STT Server Requirements
# Production dependencies for Polish HVAC CRM

# 🚀 Core FastAPI and async
fastapi==0.104.1
uvicorn[standard]==0.24.0
aiofiles==23.2.1
python-multipart==0.0.6

# 📊 Data processing and validation
pydantic==2.5.0
numpy==1.24.3
pandas==2.0.3

# 🔊 Audio processing
librosa==0.10.1
soundfile==0.12.1
scipy==1.11.4
resampy==0.4.2

# 🧠 Machine Learning (compatible with NeMo)
torch==1.13.1
torchaudio==0.13.1
torchvision==0.14.1

# 🎯 NVIDIA NeMo dependencies
omegaconf==2.3.0
hydra-core==1.3.2
pytorch-lightning==1.9.5
torchmetrics==0.11.4

# 📡 HTTP and API clients
requests==2.31.0
httpx==0.25.2
aiohttp==3.9.1

# 🔧 Utilities
python-dotenv==1.0.0
click==8.1.7
tqdm==4.66.1
colorlog==6.8.0

# 📊 Monitoring and logging
prometheus-client==0.19.0
structlog==23.2.0

# 🔒 Security
cryptography==41.0.7
python-jose[cryptography]==3.3.0

# 🧪 Development and testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
