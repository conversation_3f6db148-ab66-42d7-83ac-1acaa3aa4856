# 🎤 NVIDIA NeMo STT Server Dockerfile
# Production-ready NVIDIA NeMo FastConformer for Polish HVAC CRM

FROM nvidia/cuda:11.8-devel-ubuntu20.04

# 🔧 Environment setup
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# 📦 System dependencies
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3.9-dev \
    python3-pip \
    python3.9-venv \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    libsndfile1 \
    libsndfile1-dev \
    ffmpeg \
    sox \
    libsox-fmt-all \
    portaudio19-dev \
    libasound2-dev \
    libpulse-dev \
    && rm -rf /var/lib/apt/lists/*

# 🐍 Python setup
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.9 1
RUN python3 -m pip install --upgrade pip setuptools wheel

# 📁 Working directory
WORKDIR /app

# 📋 Copy requirements first for better caching
COPY requirements.txt .

# 🔧 Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# 🎯 Install NVIDIA NeMo with specific versions
RUN pip install --no-cache-dir \
    torch==1.13.1+cu117 \
    torchaudio==0.13.1+cu117 \
    -f https://download.pytorch.org/whl/torch_stable.html

# Install NeMo toolkit
RUN pip install --no-cache-dir nemo_toolkit[asr]==1.20.0

# 📁 Create necessary directories
RUN mkdir -p /app/logs /app/audio_input /app/transcriptions /app/models

# 📄 Copy application code
COPY nvidia_nemo_server.py .

# 🔧 Set permissions
RUN chmod +x nvidia_nemo_server.py

# 🏥 Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8889/health || exit 1

# 🚀 Expose port
EXPOSE 8889

# 🎯 Entry point
CMD ["python", "nvidia_nemo_server.py"]
