version: '3.8'

services:
  # 🎤 Simple STT Service for Testing
  simple-stt:
    build:
      context: ./simple-stt
      dockerfile: Dockerfile
    container_name: hvac-simple-stt
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis-krabulon:6379
    volumes:
      - ./audio_input:/app/audio_input
      - ./transcriptions:/app/transcriptions
      - ./logs:/app/logs
    ports:
      - "8889:8889"  # STT Service
    networks:
      - hvac-network

  # 🔄 Audio Converter Service (M4A -> WAV)
  audio-converter:
    build:
      context: ./audio-converter
      dockerfile: Dockerfile
    container_name: hvac-audio-converter
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis-krabulon:6379
      - INPUT_FORMAT=m4a
      - OUTPUT_FORMAT=wav
      - SAMPLE_RATE=16000
      - CHANNELS=1
    volumes:
      - ./audio_input:/app/input
      - ./audio_converted:/app/output
      - ./logs:/app/logs
    # depends_on: u<PERSON><PERSON><PERSON> zewnętrznego Redis
    networks:
      - hvac-network

  # 📧 Email Processor Service
  email-processor:
    build:
      context: ./email-processor
      dockerfile: Dockerfile
    container_name: hvac-email-processor
    restart: unless-stopped
    environment:
      - IMAP_SERVER=serwer2440139.home.pl
      - IMAP_PORT=993
      - EMAIL_DOLORES=<EMAIL>
      - EMAIL_GRZEGORZ=<EMAIL>
      - DOLORES_EMAIL_PASSWORD=${DOLORES_EMAIL_PASSWORD}
      - GRZEGORZ_EMAIL_PASSWORD=${GRZEGORZ_EMAIL_PASSWORD}
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=${POSTGRES_URL}
      - MINIO_ENDPOINT=**************:9000
      - MINIO_ACCESS_KEY=koldbringer
      - MINIO_SECRET_KEY=Blaeritipol1
    volumes:
      - ./email_attachments:/app/attachments
      - ./logs:/app/logs
    depends_on:
      - audio-converter
    networks:
      - hvac-network

  # 🧠 Gemma 3 4B Integration Service
  gemma-integration:
    build:
      context: ./gemma-integration
      dockerfile: Dockerfile
    container_name: hvac-gemma-integration
    restart: unless-stopped
    environment:
      - LM_STUDIO_URL=http://*************:1234
      - MODEL_NAME=gemma-3-4b
      - LANGUAGE=pl
      - MAX_TOKENS=2048
      - TEMPERATURE=0.3
      - REDIS_URL=redis://redis-krabulon:6379
    volumes:
      - ./transcriptions:/app/transcriptions
      - ./analysis_results:/app/analysis
      - ./logs:/app/logs
    # depends_on: używamy zewnętrznego Redis
    networks:
      - hvac-network

  # 📊 Redis Cache - używamy zewnętrznego Redis na localhost:6379

  # 🎯 Transcription Orchestrator
  transcription-orchestrator:
    build:
      context: ./orchestrator
      dockerfile: Dockerfile
    container_name: hvac-transcription-orchestrator
    restart: unless-stopped
    environment:
      - NVIDIA_STT_URL=http://simple-stt:8889
      - AUDIO_CONVERTER_URL=http://audio-converter:8080
      - EMAIL_PROCESSOR_URL=http://email-processor:8080
      - GEMMA_INTEGRATION_URL=http://gemma-integration:8080
      - GOBACKEND_URL=http://host.docker.internal:8080
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=**********************************************************
    ports:
      - "9000:8080"  # Main API
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./audio_input:/app/audio_input
      - ./audio_converted:/app/audio_converted
      - ./transcriptions:/app/transcriptions
    depends_on:
      - audio-converter
      - email-processor
      - gemma-integration
    networks:
      - hvac-network

# volumes: używamy zewnętrznego Redis

networks:
  hvac-network:
    external: true
    name: python_mixer_hvac-network
