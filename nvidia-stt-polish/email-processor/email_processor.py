#!/usr/bin/env python3
"""
📧 Email Processor Service
Automatyczne pobieranie i przetwarzanie emaili z załącznikami M4A
<NAME_EMAIL> i <EMAIL>
"""

import os
import asyncio
import logging
import json
import email
import imaplib
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import base64

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
from redis import Redis
import aiofiles
import asyncpg
from minio import Minio
import requests

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/email_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🎯 Modele danych
class EmailAccount(BaseModel):
    email: str
    password: str
    imap_server: str = "serwer2440139.home.pl"
    imap_port: int = 993
    account_type: str  # "transcription" lub "customer"

class ProcessedEmail(BaseModel):
    message_id: str
    from_email: str
    to_email: str
    subject: str
    body: str
    attachments: List[Dict]
    processing_time: float
    account_type: str

class EmailProcessorService:
    """📧 Serwis przetwarzania emaili HVAC"""
    
    def __init__(self):
        self.app = FastAPI(
            title="📧 HVAC Email Processor",
            description="Automatyczne przetwarzanie emaili z załącznikami M4A",
            version="1.0.0"
        )
        
        # 🔧 Konfiguracja CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 🔧 Konfiguracja połączeń
        self.redis_client = None
        self.postgres_pool = None
        self.minio_client = None
        
        # 📧 Konfiguracja kont email
        self.email_accounts = {
            "transcription": EmailAccount(
                email="<EMAIL>",
                password=os.getenv("DOLORES_EMAIL_PASSWORD", ""),
                imap_server="serwer2440139.home.pl",
                account_type="transcription"
            ),
            "customer": EmailAccount(
                email="<EMAIL>",
                password=os.getenv("GRZEGORZ_EMAIL_PASSWORD", ""),
                imap_server="serwer2440139.home.pl",
                account_type="customer"
            )
        }
        
        # 🎵 Obsługiwane formaty audio
        self.audio_formats = ['.m4a', '.mp3', '.wav', '.aac', '.ogg']
        
        self._setup_routes()
    
    async def initialize(self):
        """🚀 Inicjalizacja serwisu"""
        logger.info("🚀 Inicjalizacja Email Processor Service...")
        
        try:
            # 📡 Redis
            redis_url = os.getenv("REDIS_URL", "redis://redis-krabulon:6379")
            self.redis_client = Redis.from_url(redis_url, decode_responses=True)
            self.redis_client.ping()
            logger.info("✅ Połączenie z Redis nawiązane")
            
            # 🐘 PostgreSQL
            postgres_url = os.getenv("POSTGRES_URL")
            if postgres_url:
                self.postgres_pool = await asyncpg.create_pool(postgres_url)
                logger.info("✅ Połączenie z PostgreSQL nawiązane")
            
            # 📦 MinIO
            minio_endpoint = os.getenv("MINIO_ENDPOINT")
            if minio_endpoint:
                self.minio_client = Minio(
                    minio_endpoint,
                    access_key=os.getenv("MINIO_ACCESS_KEY"),
                    secret_key=os.getenv("MINIO_SECRET_KEY"),
                    secure=False
                )
                logger.info("✅ Połączenie z MinIO nawiązane")
            
            # 📁 Katalogi
            self._ensure_directories()
            
            # 🔄 Uruchomienie zadań w tle
            asyncio.create_task(self._email_monitoring_loop())
            
            logger.info("✅ Email Processor Service zainicjalizowany!")
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji: {e}")
            raise
    
    def _ensure_directories(self):
        """📁 Sprawdzenie katalogów"""
        directories = ['/app/attachments', '/app/logs', '/app/temp']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _setup_routes(self):
        """🛣️ Konfiguracja endpointów"""
        
        @self.app.on_event("startup")
        async def startup_event():
            await self.initialize()
        
        @self.app.get("/health")
        async def health_check():
            """🏥 Status serwisu"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "email_accounts": list(self.email_accounts.keys()),
                "audio_formats": self.audio_formats,
                "connections": {
                    "redis": self.redis_client is not None,
                    "postgres": self.postgres_pool is not None,
                    "minio": self.minio_client is not None
                }
            }
        
        @self.app.post("/process/manual")
        async def process_emails_manually(
            background_tasks: BackgroundTasks,
            account_type: str = "all"
        ):
            """🔧 Ręczne przetwarzanie emaili"""
            if account_type == "all":
                accounts = list(self.email_accounts.keys())
            else:
                accounts = [account_type] if account_type in self.email_accounts else []
            
            results = []
            for account in accounts:
                result = await self._process_account_emails(account)
                results.append({"account": account, "result": result})
            
            return {"processed_accounts": results}
        
        @self.app.get("/stats")
        async def get_processing_stats():
            """📊 Statystyki przetwarzania"""
            try:
                stats = {}
                for account_type in self.email_accounts.keys():
                    cache_key = f"email_stats:{account_type}"
                    cached_stats = self.redis_client.get(cache_key)
                    if cached_stats:
                        stats[account_type] = json.loads(cached_stats)
                    else:
                        stats[account_type] = {"processed": 0, "attachments": 0, "last_check": None}
                
                return stats
            except Exception as e:
                logger.error(f"❌ Błąd pobierania statystyk: {e}")
                return {}
    
    async def _email_monitoring_loop(self):
        """🔄 Pętla monitorowania emaili"""
        logger.info("🔄 Uruchomienie monitorowania emaili...")
        
        while True:
            try:
                for account_type in self.email_accounts.keys():
                    await self._process_account_emails(account_type)
                
                # Czekanie 5 minut między sprawdzeniami
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"❌ Błąd w pętli monitorowania: {e}")
                await asyncio.sleep(60)  # Krótsze oczekiwanie przy błędzie
    
    async def _process_account_emails(self, account_type: str) -> Dict:
        """📧 Przetwarzanie emaili z konkretnego konta"""
        if account_type not in self.email_accounts:
            raise ValueError(f"Nieznany typ konta: {account_type}")
        
        account = self.email_accounts[account_type]
        logger.info(f"📧 Przetwarzanie emaili z konta: {account.email}")
        
        start_time = time.time()
        processed_count = 0
        attachments_count = 0
        
        try:
            # 📡 Połączenie z IMAP
            mail = imaplib.IMAP4_SSL(account.imap_server, account.imap_port)
            mail.login(account.email, account.password)
            mail.select('INBOX')
            
            # 🔍 Wyszukiwanie nowych emaili (ostatnie 24h)
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%d-%b-%Y")
            search_criteria = f'(SINCE "{yesterday}" UNSEEN)'
            
            status, messages = mail.search(None, search_criteria)
            
            if status == 'OK':
                message_ids = messages[0].split()
                logger.info(f"📬 Znaleziono {len(message_ids)} nowych emaili")
                
                for msg_id in message_ids:
                    try:
                        # 📥 Pobieranie emaila
                        status, msg_data = mail.fetch(msg_id, '(RFC822)')
                        
                        if status == 'OK':
                            email_body = msg_data[0][1]
                            email_message = email.message_from_bytes(email_body)
                            
                            # 🔄 Przetwarzanie emaila
                            result = await self._process_single_email(email_message, account_type)
                            
                            if result:
                                processed_count += 1
                                attachments_count += len(result.get('attachments', []))
                                
                                # 💾 Zapisanie do bazy danych
                                await self._save_email_to_database(result)
                                
                                # ✅ Oznaczenie jako przeczytane
                                mail.store(msg_id, '+FLAGS', '\\Seen')
                    
                    except Exception as e:
                        logger.error(f"❌ Błąd przetwarzania emaila {msg_id}: {e}")
            
            mail.close()
            mail.logout()
            
            processing_time = time.time() - start_time
            
            # 📊 Aktualizacja statystyk
            await self._update_processing_stats(account_type, processed_count, attachments_count)
            
            logger.info(f"✅ Przetworzono {processed_count} emaili z {attachments_count} załącznikami ({processing_time:.2f}s)")
            
            return {
                "processed_emails": processed_count,
                "attachments_found": attachments_count,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"❌ Błąd przetwarzania konta {account_type}: {e}")
            return {"error": str(e)}
    
    async def _process_single_email(self, email_message, account_type: str) -> Optional[Dict]:
        """📧 Przetwarzanie pojedynczego emaila"""
        try:
            # 📋 Podstawowe informacje
            from_email = email_message.get('From', '')
            to_email = email_message.get('To', '')
            subject = email_message.get('Subject', '')
            message_id = email_message.get('Message-ID', '')
            
            # 📝 Treść emaila
            body = self._extract_email_body(email_message)
            
            # 📎 Załączniki
            attachments = []
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        
                        if filename and any(filename.lower().endswith(fmt) for fmt in self.audio_formats):
                            # 💾 Zapisanie załącznika
                            attachment_path = await self._save_attachment(part, filename, account_type)
                            
                            if attachment_path:
                                attachments.append({
                                    "filename": filename,
                                    "path": attachment_path,
                                    "size": len(part.get_payload(decode=True)),
                                    "type": "audio"
                                })
                                
                                # 🔄 Wysłanie do konwersji
                                await self._send_to_audio_converter(attachment_path, filename)
            
            if attachments or account_type == "customer":  # Zawsze przetwarzaj emaile klientów
                return {
                    "message_id": message_id,
                    "from_email": from_email,
                    "to_email": to_email,
                    "subject": subject,
                    "body": body,
                    "attachments": attachments,
                    "account_type": account_type,
                    "processed_at": datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Błąd przetwarzania emaila: {e}")
            return None
    
    def _extract_email_body(self, email_message) -> str:
        """📝 Wyodrębnienie treści emaila"""
        body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    body += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        return body.strip()
    
    async def _save_attachment(self, part, filename: str, account_type: str) -> Optional[str]:
        """💾 Zapisanie załącznika"""
        try:
            # 📁 Ścieżka zapisu
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_filename = f"{timestamp}_{filename}"
            attachment_path = f"/app/attachments/{account_type}_{safe_filename}"
            
            # 💾 Zapisanie pliku
            attachment_data = part.get_payload(decode=True)
            async with aiofiles.open(attachment_path, 'wb') as f:
                await f.write(attachment_data)
            
            # 📦 Upload do MinIO jeśli dostępne
            if self.minio_client:
                try:
                    bucket_name = f"hvac-{account_type}-attachments"
                    self.minio_client.put_object(
                        bucket_name,
                        safe_filename,
                        data=attachment_data,
                        length=len(attachment_data)
                    )
                    logger.info(f"📦 Załącznik przesłany do MinIO: {safe_filename}")
                except Exception as e:
                    logger.warning(f"⚠️ Błąd przesyłania do MinIO: {e}")
            
            logger.info(f"💾 Zapisano załącznik: {attachment_path}")
            return attachment_path
            
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania załącznika: {e}")
            return None
    
    async def _send_to_audio_converter(self, file_path: str, filename: str):
        """🔄 Wysłanie pliku do konwersji"""
        try:
            converter_url = "http://audio-converter:8080/convert"
            
            with open(file_path, 'rb') as f:
                files = {'audio_file': (filename, f, 'audio/m4a')}
                data = {
                    'output_format': 'wav',
                    'sample_rate': 16000,
                    'channels': 1,
                    'normalize': True
                }
                
                response = requests.post(converter_url, files=files, data=data, timeout=300)
                
                if response.status_code == 200:
                    logger.info(f"✅ Plik wysłany do konwersji: {filename}")
                else:
                    logger.error(f"❌ Błąd konwersji {filename}: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"❌ Błąd wysyłania do konwersji: {e}")
    
    async def _save_email_to_database(self, email_data: Dict):
        """💾 Zapisanie emaila do bazy danych"""
        if not self.postgres_pool:
            return
        
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO email_communications 
                    (message_id, from_email, to_email, subject, body, attachments_count, account_type, processed_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (message_id) DO NOTHING
                """, 
                email_data['message_id'],
                email_data['from_email'],
                email_data['to_email'],
                email_data['subject'],
                email_data['body'],
                len(email_data['attachments']),
                email_data['account_type'],
                datetime.now()
                )
                
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania do bazy: {e}")
    
    async def _update_processing_stats(self, account_type: str, processed: int, attachments: int):
        """📊 Aktualizacja statystyk"""
        try:
            cache_key = f"email_stats:{account_type}"
            stats = {
                "processed": processed,
                "attachments": attachments,
                "last_check": datetime.now().isoformat()
            }
            
            self.redis_client.setex(cache_key, 3600, json.dumps(stats))
            
        except Exception as e:
            logger.warning(f"⚠️ Błąd aktualizacji statystyk: {e}")

# 🚀 Uruchomienie serwisu
if __name__ == "__main__":
    service = EmailProcessorService()
    
    uvicorn.run(
        service.app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
