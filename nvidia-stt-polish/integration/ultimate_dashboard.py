#!/usr/bin/env python3
"""
📊 Ultimate Dashboard - 2024 Edition
Next-generation monitoring dashboard with AI-powered insights and real-time analytics
"""

import asyncio
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from ultimate_system_integrator import UltimateSystemIntegrator

# 🎨 Page configuration
st.set_page_config(
    page_title="🚀 NVIDIA STT Ultimate Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 🎨 Custom CSS for 2024 design
st.markdown("""
<style>
.main-header {
    font-size: 3rem;
    background: linear-gradient(90deg, #1f77b4, #ff7f0e, #2ca02c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    margin-bottom: 2rem;
    font-weight: bold;
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1.5rem;
    border-radius: 15px;
    color: white;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin: 0.5rem 0;
}

.status-healthy {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.status-degraded {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.status-critical {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.ai-insight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
    border-radius: 10px;
    margin: 0.5rem 0;
    color: white;
}

.recommendation-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 1rem;
    border-radius: 10px;
    margin: 0.5rem 0;
    border-left: 4px solid #ff6b6b;
}

.performance-excellent {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.performance-good {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.performance-poor {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}
</style>
""", unsafe_allow_html=True)

class UltimateDashboard:
    """📊 Ultimate Dashboard with 2024 features"""
    
    def __init__(self):
        self.integrator = UltimateSystemIntegrator()
        
    async def render_dashboard(self):
        """🎨 Renderowanie ultimate dashboard"""
        
        # Header
        st.markdown('<h1 class="main-header">🚀 NVIDIA STT Ultimate Dashboard 2024</h1>', 
                   unsafe_allow_html=True)
        
        # Sidebar controls
        self.render_sidebar()
        
        # Get dashboard data
        try:
            dashboard_data = await self.integrator.generate_ultimate_dashboard_data()
        except Exception as e:
            st.error(f"❌ Failed to load dashboard data: {e}")
            return
        
        # Main dashboard sections
        self.render_system_overview(dashboard_data.get("system_overview", {}))
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            self.render_real_time_metrics(dashboard_data.get("real_time_metrics", {}))
            self.render_historical_trends(dashboard_data.get("historical_trends", {}))
        
        with col2:
            self.render_service_health(dashboard_data.get("service_health", {}))
            self.render_ai_insights(dashboard_data.get("ai_insights", {}))
        
        # Full width sections
        self.render_recommendations(dashboard_data.get("recommendations", []))
        self.render_alerts_and_optimization(
            dashboard_data.get("alerts", []),
            dashboard_data.get("optimization_opportunities", [])
        )
    
    def render_sidebar(self):
        """🎛️ Sidebar z kontrolkami"""
        
        st.sidebar.markdown("## 🎛️ Dashboard Controls")
        
        # Auto refresh
        auto_refresh = st.sidebar.checkbox("🔄 Auto Refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 300, 60)
        
        # Manual refresh
        if st.sidebar.button("🔄 Refresh Now", type="primary"):
            st.experimental_rerun()
        
        # Time range selector
        st.sidebar.markdown("### 📅 Time Range")
        time_range = st.sidebar.selectbox(
            "Select Range",
            ["Last Hour", "Last 6 Hours", "Last 24 Hours", "Last 7 Days"]
        )
        
        # Metric filters
        st.sidebar.markdown("### 📊 Metrics Filter")
        show_accuracy = st.sidebar.checkbox("📝 Accuracy Metrics", value=True)
        show_performance = st.sidebar.checkbox("⚡ Performance Metrics", value=True)
        show_resources = st.sidebar.checkbox("🖥️ Resource Metrics", value=True)
        
        # Alert settings
        st.sidebar.markdown("### 🚨 Alert Settings")
        alert_threshold = st.sidebar.slider("Alert Threshold", 0.0, 1.0, 0.8)
        
        # Export options
        st.sidebar.markdown("### 📤 Export")
        if st.sidebar.button("📊 Export Report"):
            st.sidebar.success("Report exported!")
        
        if st.sidebar.button("📈 Export Charts"):
            st.sidebar.success("Charts exported!")
    
    def render_system_overview(self, overview_data: dict):
        """🎯 System overview section"""
        
        st.markdown("## 🎯 System Overview")
        
        col1, col2, col3, col4, col5 = st.columns(5)
        
        # Overall status
        with col1:
            status = overview_data.get("status", "unknown")
            status_class = f"status-{status}" if status in ["healthy", "degraded", "critical"] else "metric-card"
            
            st.markdown(f"""
            <div class="metric-card {status_class}">
                <h3>🎯 Status</h3>
                <h2>{status.upper()}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Performance grade
        with col2:
            grade = overview_data.get("grade", "N/A")
            grade_class = "performance-excellent" if grade in ["A+", "A"] else \
                         "performance-good" if grade in ["B+", "B"] else "performance-poor"
            
            st.markdown(f"""
            <div class="metric-card {grade_class}">
                <h3>🏆 Grade</h3>
                <h2>{grade}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Uptime
        with col3:
            uptime = overview_data.get("uptime", "N/A")
            st.markdown(f"""
            <div class="metric-card">
                <h3>⏱️ Uptime</h3>
                <h2>{uptime}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Total requests
        with col4:
            requests = overview_data.get("total_requests", 0)
            st.markdown(f"""
            <div class="metric-card">
                <h3>📊 Requests</h3>
                <h2>{requests:,}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Last updated
        with col5:
            last_updated = overview_data.get("last_updated", "")
            if last_updated:
                update_time = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                time_ago = datetime.now() - update_time.replace(tzinfo=None)
                time_str = f"{int(time_ago.total_seconds())}s ago"
            else:
                time_str = "N/A"
            
            st.markdown(f"""
            <div class="metric-card">
                <h3>🕐 Updated</h3>
                <h2>{time_str}</h2>
            </div>
            """, unsafe_allow_html=True)
    
    def render_real_time_metrics(self, metrics_data: dict):
        """📊 Real-time metrics visualization"""
        
        st.markdown("## 📊 Real-Time Performance Metrics")
        
        if not metrics_data:
            st.warning("📊 No real-time metrics available")
            return
        
        # Create metrics charts
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Accuracy Metrics", "Performance Metrics", "Resource Usage", "Quality Scores"),
            specs=[[{"type": "indicator"}, {"type": "indicator"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )
        
        # Accuracy gauge
        accuracy_data = metrics_data.get("accuracy", {})
        word_acc = accuracy_data.get("word_accuracy", 0)
        
        fig.add_trace(
            go.Indicator(
                mode="gauge+number+delta",
                value=word_acc,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Word Accuracy (%)"},
                delta={'reference': 95},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 70], 'color': "lightgray"},
                        {'range': [70, 85], 'color': "yellow"},
                        {'range': [85, 95], 'color': "orange"},
                        {'range': [95, 100], 'color': "green"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 95
                    }
                }
            ),
            row=1, col=1
        )
        
        # Performance gauge
        performance_data = metrics_data.get("performance", {})
        proc_time = performance_data.get("processing_time", 0)
        
        fig.add_trace(
            go.Indicator(
                mode="gauge+number+delta",
                value=proc_time,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Processing Time (s)"},
                delta={'reference': 30},
                gauge={
                    'axis': {'range': [0, 60]},
                    'bar': {'color': "darkgreen"},
                    'steps': [
                        {'range': [0, 10], 'color': "green"},
                        {'range': [10, 30], 'color': "yellow"},
                        {'range': [30, 60], 'color': "red"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 30
                    }
                }
            ),
            row=1, col=2
        )
        
        # Resource usage bar chart
        resources_data = metrics_data.get("resources", {})
        resource_names = ["CPU", "Memory", "GPU", "Disk"]
        resource_values = [
            resources_data.get("cpu_usage", 0),
            resources_data.get("memory_usage", 0),
            resources_data.get("gpu_utilization", 0),
            resources_data.get("disk_usage", 0)
        ]
        
        fig.add_trace(
            go.Bar(
                x=resource_names,
                y=resource_values,
                marker_color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'],
                name="Resource Usage"
            ),
            row=2, col=1
        )
        
        # Quality trend (mock data for now)
        quality_timestamps = [datetime.now() - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        quality_scores = np.random.normal(90, 5, 12)  # Mock quality scores
        
        fig.add_trace(
            go.Scatter(
                x=quality_timestamps,
                y=quality_scores,
                mode='lines+markers',
                name='Quality Score',
                line=dict(color='purple', width=3)
            ),
            row=2, col=2
        )
        
        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    def render_historical_trends(self, trends_data: dict):
        """📈 Historical trends visualization"""
        
        st.markdown("## 📈 Historical Trends (24h)")
        
        if not trends_data or trends_data.get("status") == "insufficient_data":
            st.info("📈 Insufficient historical data. Run system for longer to see trends.")
            return
        
        # Create trends chart
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Word Accuracy", "Processing Time", "RTF Factor", "GPU Utilization"),
            vertical_spacing=0.1
        )
        
        timestamps = [datetime.fromisoformat(ts) for ts in trends_data.get("timestamps", [])]
        
        # Word accuracy trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("word_accuracy", []),
                mode='lines+markers',
                name='Word Accuracy',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        # Processing time trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("processing_time", []),
                mode='lines+markers',
                name='Processing Time',
                line=dict(color='orange', width=2)
            ),
            row=1, col=2
        )
        
        # RTF factor trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("rtf_factor", []),
                mode='lines+markers',
                name='RTF Factor',
                line=dict(color='green', width=2)
            ),
            row=2, col=1
        )
        
        # GPU utilization trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("gpu_utilization", []),
                mode='lines+markers',
                name='GPU Utilization',
                line=dict(color='red', width=2)
            ),
            row=2, col=2
        )
        
        fig.update_layout(height=500, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    def render_service_health(self, services_data: dict):
        """🏥 Service health status"""
        
        st.markdown("## 🏥 Service Health")
        
        for service_name, service_data in services_data.items():
            status = service_data.get("status", "unknown")
            
            # Status icon and color
            if status == "healthy":
                icon = "✅"
                color = "green"
            elif status == "degraded":
                icon = "⚠️"
                color = "orange"
            elif status == "critical":
                icon = "🔴"
                color = "red"
            else:
                icon = "❓"
                color = "gray"
            
            # Service card
            with st.expander(f"{icon} {service_name.replace('_', ' ').title()}", expanded=True):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Status:** {status}")
                    if "response_time" in service_data:
                        st.write(f"**Response Time:** {service_data['response_time']:.3f}s")
                
                with col2:
                    if "last_check" in service_data:
                        st.write(f"**Last Check:** {service_data['last_check']}")
                    if "error" in service_data:
                        st.error(f"Error: {service_data['error']}")
    
    def render_ai_insights(self, insights_data: dict):
        """🧠 AI insights section"""
        
        st.markdown("## 🧠 AI Insights")
        
        # Drift detection
        drift_data = insights_data.get("drift_detection", {})
        if drift_data.get("drift_detected"):
            st.markdown(f"""
            <div class="ai-insight">
                <h4>📊 Data Drift Detected</h4>
                <p>Magnitude: {drift_data.get('drift_magnitude', 0):.3f}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Anomaly detection
        anomaly_data = insights_data.get("anomaly_detection", {})
        if anomaly_data.get("anomalies_detected"):
            st.markdown(f"""
            <div class="ai-insight">
                <h4>🔍 Anomalies Detected</h4>
                <p>Count: {anomaly_data.get('anomaly_count', 0)}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Performance trends
        trends = insights_data.get("performance_trends", {})
        if trends.get("status") == "analyzed":
            st.markdown(f"""
            <div class="ai-insight">
                <h4>📈 Performance Trends</h4>
                <p>Accuracy: {trends.get('accuracy_trend', 'stable')}</p>
                <p>Performance: {trends.get('performance_trend', 'stable')}</p>
            </div>
            """, unsafe_allow_html=True)
    
    def render_recommendations(self, recommendations: list):
        """💡 Recommendations section"""
        
        st.markdown("## 💡 Smart Recommendations")
        
        if not recommendations:
            st.info("💡 No recommendations at this time. System is performing well!")
            return
        
        # Group recommendations by priority
        high_priority = [r for r in recommendations if r.get("priority") == "high"]
        medium_priority = [r for r in recommendations if r.get("priority") == "medium"]
        low_priority = [r for r in recommendations if r.get("priority") == "low"]
        
        # High priority recommendations
        if high_priority:
            st.markdown("### 🔴 High Priority")
            for rec in high_priority:
                st.markdown(f"""
                <div class="recommendation-card">
                    <h4>{rec.get('action', 'No action specified')}</h4>
                    <p><strong>Type:</strong> {rec.get('type', 'general')}</p>
                    <p><strong>Impact:</strong> {rec.get('estimated_impact', 'unknown')}</p>
                </div>
                """, unsafe_allow_html=True)
        
        # Medium priority recommendations
        if medium_priority:
            st.markdown("### 🟡 Medium Priority")
            for rec in medium_priority:
                st.markdown(f"""
                <div class="recommendation-card">
                    <h4>{rec.get('action', 'No action specified')}</h4>
                    <p><strong>Type:</strong> {rec.get('type', 'general')}</p>
                </div>
                """, unsafe_allow_html=True)
    
    def render_alerts_and_optimization(self, alerts: list, optimizations: list):
        """🚨 Alerts and optimization opportunities"""
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("## 🚨 Recent Alerts")
            
            if not alerts:
                st.success("🎉 No recent alerts!")
            else:
                for alert in alerts[-5:]:  # Show last 5 alerts
                    level = alert.get("level", "info")
                    message = alert.get("message", "No message")
                    timestamp = alert.get("timestamp", "")
                    
                    if level == "critical":
                        st.error(f"🔴 {message} ({timestamp})")
                    elif level == "warning":
                        st.warning(f"🟡 {message} ({timestamp})")
                    else:
                        st.info(f"🔵 {message} ({timestamp})")
        
        with col2:
            st.markdown("## 🎯 Optimization Opportunities")
            
            if not optimizations:
                st.success("🎉 System is well optimized!")
            else:
                for opt in optimizations:
                    impact = opt.get("impact", "unknown")
                    effort = opt.get("effort", "unknown")
                    description = opt.get("description", "No description")
                    
                    st.markdown(f"""
                    **{description}**  
                    Impact: {impact} | Effort: {effort}
                    """)

# 🚀 Main dashboard execution
async def main():
    """🎯 Main dashboard function"""
    
    dashboard = UltimateDashboard()
    await dashboard.render_dashboard()

# Run the dashboard
if __name__ == "__main__":
    asyncio.run(main())
