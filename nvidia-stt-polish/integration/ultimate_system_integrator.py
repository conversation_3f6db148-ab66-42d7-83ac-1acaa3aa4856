#!/usr/bin/env python3
"""
🚀 Ultimate System Integrator
Next-level integration of all NVIDIA STT HVAC functionalities with 2024 best practices
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import aiohttp
import aiofiles
from pydantic import BaseModel
import numpy as np
import pandas as pd

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemStatus(Enum):
    """🎯 Status systemu"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CRITICAL = "critical"
    OFFLINE = "offline"

class PerformanceGrade(Enum):
    """🏆 Oceny wydajności"""
    EXCELLENT = "A+"  # 95%+
    VERY_GOOD = "A"   # 90-94%
    GOOD = "B+"       # 85-89%
    ACCEPTABLE = "B"  # 80-84%
    NEEDS_WORK = "C"  # 70-79%
    POOR = "D"        # <70%

@dataclass
class SystemMetrics:
    """📊 Metryki systemu"""
    timestamp: datetime
    word_accuracy: float
    hvac_accuracy: float
    processing_time: float
    confidence_score: float
    rtf_factor: float  # Real-time factor
    gpu_utilization: float
    memory_usage: float
    error_rate: float
    throughput: float  # requests per second

class UltimateSystemIntegrator:
    """🚀 Ultimate System Integrator - Next Level Integration"""
    
    def __init__(self):
        # 🎯 Service endpoints
        self.services = {
            "nvidia_stt": "http://localhost:8889",
            "orchestrator": "http://localhost:9000", 
            "gemma": "http://*************:1234",
            "gobackend": "http://localhost:8080",
            "hvac_remix": "http://localhost:3000"
        }
        
        # 📊 Performance thresholds (2024 standards)
        self.thresholds = {
            "word_accuracy_min": 95.0,      # A+ grade requirement
            "hvac_accuracy_min": 92.0,      # HVAC specialization
            "processing_time_max": 30.0,    # Seconds
            "rtf_factor_min": 2000,         # Real-time factor
            "confidence_min": 0.85,         # Confidence threshold
            "error_rate_max": 0.01,         # 1% max error rate
            "gpu_utilization_optimal": 80.0  # Optimal GPU usage
        }
        
        # 🧠 AI-powered monitoring
        self.ai_insights = {
            "drift_detection": True,
            "anomaly_detection": True,
            "predictive_maintenance": True,
            "auto_optimization": True
        }
        
        # 📈 Historical data
        self.metrics_history: List[SystemMetrics] = []
        self.alerts_history: List[Dict] = []
        
    async def perform_ultimate_health_check(self) -> Dict[str, Any]:
        """🏥 Ultimate health check z 2024 standards"""
        
        logger.info("🚀 Performing Ultimate Health Check...")
        
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": SystemStatus.HEALTHY.value,
            "services": {},
            "performance_metrics": {},
            "ai_insights": {},
            "recommendations": [],
            "grade": None
        }
        
        # 🎯 Check all services
        for service_name, url in self.services.items():
            try:
                service_health = await self._check_service_health(service_name, url)
                health_report["services"][service_name] = service_health
            except Exception as e:
                health_report["services"][service_name] = {
                    "status": SystemStatus.OFFLINE.value,
                    "error": str(e)
                }
        
        # 📊 Collect performance metrics
        performance_metrics = await self._collect_performance_metrics()
        health_report["performance_metrics"] = performance_metrics
        
        # 🧠 AI-powered insights
        ai_insights = await self._generate_ai_insights(performance_metrics)
        health_report["ai_insights"] = ai_insights
        
        # 🎯 Calculate overall grade
        grade = self._calculate_system_grade(performance_metrics)
        health_report["grade"] = grade.value
        
        # 📝 Generate recommendations
        recommendations = await self._generate_smart_recommendations(
            health_report["services"], performance_metrics, ai_insights
        )
        health_report["recommendations"] = recommendations
        
        # 🚨 Determine overall status
        overall_status = self._determine_overall_status(
            health_report["services"], performance_metrics
        )
        health_report["overall_status"] = overall_status.value
        
        logger.info(f"✅ Health check completed - Status: {overall_status.value}, Grade: {grade.value}")
        return health_report
    
    async def _check_service_health(self, service_name: str, url: str) -> Dict:
        """🔍 Sprawdzenie zdrowia pojedynczego serwisu"""
        
        try:
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                
                # Different health endpoints for different services
                health_endpoint = "/health"
                if service_name == "gemma":
                    health_endpoint = "/v1/models"
                elif service_name == "hvac_remix":
                    health_endpoint = "/api/health"
                
                async with session.get(f"{url}{health_endpoint}", timeout=5) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            "status": SystemStatus.HEALTHY.value,
                            "response_time": response_time,
                            "details": data,
                            "last_check": datetime.now().isoformat()
                        }
                    else:
                        return {
                            "status": SystemStatus.DEGRADED.value,
                            "response_time": response_time,
                            "http_status": response.status,
                            "last_check": datetime.now().isoformat()
                        }
        
        except asyncio.TimeoutError:
            return {
                "status": SystemStatus.CRITICAL.value,
                "error": "Timeout",
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": SystemStatus.OFFLINE.value,
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    async def _collect_performance_metrics(self) -> Dict:
        """📊 Zbieranie metryk wydajności"""
        
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "accuracy": {},
            "performance": {},
            "resources": {},
            "quality": {}
        }
        
        try:
            # 🎤 STT metrics
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.services['nvidia_stt']}/stats") as response:
                    if response.status == 200:
                        stt_stats = await response.json()
                        
                        metrics["accuracy"] = {
                            "word_accuracy": stt_stats.get("average_word_accuracy", 0),
                            "hvac_accuracy": stt_stats.get("average_hvac_accuracy", 0),
                            "confidence": stt_stats.get("average_confidence", 0)
                        }
                        
                        metrics["performance"] = {
                            "processing_time": stt_stats.get("average_processing_time", 0),
                            "rtf_factor": self._calculate_rtf_factor(stt_stats),
                            "throughput": stt_stats.get("requests_per_second", 0)
                        }
        
        except Exception as e:
            logger.warning(f"⚠️ Could not collect STT metrics: {e}")
        
        # 🖥️ System resources
        try:
            import psutil
            
            metrics["resources"] = {
                "cpu_usage": psutil.cpu_percent(),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "gpu_utilization": await self._get_gpu_utilization()
            }
        
        except Exception as e:
            logger.warning(f"⚠️ Could not collect resource metrics: {e}")
        
        return metrics
    
    def _calculate_rtf_factor(self, stt_stats: Dict) -> float:
        """⚡ Obliczenie Real-Time Factor (2024 standard)"""
        
        processing_time = stt_stats.get("average_processing_time", 1.0)
        audio_duration = stt_stats.get("average_audio_duration", 3.0)  # Assume 3s average
        
        if processing_time > 0:
            rtf = audio_duration / processing_time
            return rtf
        return 0.0
    
    async def _get_gpu_utilization(self) -> float:
        """🎮 Sprawdzenie wykorzystania GPU"""
        
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                return gpus[0].load * 100
        except:
            pass
        
        return 0.0
    
    async def _generate_ai_insights(self, metrics: Dict) -> Dict:
        """🧠 Generowanie AI insights (2024 standards)"""
        
        insights = {
            "drift_detection": await self._detect_data_drift(metrics),
            "anomaly_detection": await self._detect_anomalies(metrics),
            "performance_trends": await self._analyze_performance_trends(),
            "optimization_opportunities": await self._identify_optimizations(metrics),
            "predictive_maintenance": await self._predict_maintenance_needs(metrics)
        }
        
        return insights
    
    async def _detect_data_drift(self, current_metrics: Dict) -> Dict:
        """📊 Wykrywanie data drift"""
        
        if len(self.metrics_history) < 10:
            return {"status": "insufficient_data", "drift_detected": False}
        
        # Porównanie z historical baseline
        recent_accuracy = [m.word_accuracy for m in self.metrics_history[-10:]]
        current_accuracy = current_metrics.get("accuracy", {}).get("word_accuracy", 0)
        
        baseline_mean = np.mean(recent_accuracy)
        drift_threshold = 0.05  # 5% drift threshold
        
        drift_detected = abs(current_accuracy - baseline_mean) > drift_threshold
        
        return {
            "status": "analyzed",
            "drift_detected": drift_detected,
            "current_accuracy": current_accuracy,
            "baseline_mean": baseline_mean,
            "drift_magnitude": abs(current_accuracy - baseline_mean)
        }
    
    async def _detect_anomalies(self, metrics: Dict) -> Dict:
        """🔍 Wykrywanie anomalii"""
        
        anomalies = []
        
        # Check accuracy anomalies
        word_acc = metrics.get("accuracy", {}).get("word_accuracy", 0)
        if word_acc < self.thresholds["word_accuracy_min"]:
            anomalies.append({
                "type": "accuracy_degradation",
                "metric": "word_accuracy",
                "value": word_acc,
                "threshold": self.thresholds["word_accuracy_min"],
                "severity": "high"
            })
        
        # Check performance anomalies
        proc_time = metrics.get("performance", {}).get("processing_time", 0)
        if proc_time > self.thresholds["processing_time_max"]:
            anomalies.append({
                "type": "performance_degradation",
                "metric": "processing_time",
                "value": proc_time,
                "threshold": self.thresholds["processing_time_max"],
                "severity": "medium"
            })
        
        return {
            "anomalies_detected": len(anomalies) > 0,
            "anomaly_count": len(anomalies),
            "anomalies": anomalies
        }
    
    async def _analyze_performance_trends(self) -> Dict:
        """📈 Analiza trendów wydajności"""
        
        if len(self.metrics_history) < 5:
            return {"status": "insufficient_data"}
        
        # Analiza trendów z ostatnich 24h
        recent_metrics = self.metrics_history[-24:] if len(self.metrics_history) >= 24 else self.metrics_history
        
        accuracy_trend = np.polyfit(range(len(recent_metrics)), 
                                   [m.word_accuracy for m in recent_metrics], 1)[0]
        
        performance_trend = np.polyfit(range(len(recent_metrics)),
                                     [m.processing_time for m in recent_metrics], 1)[0]
        
        return {
            "status": "analyzed",
            "accuracy_trend": "improving" if accuracy_trend > 0 else "declining",
            "performance_trend": "improving" if performance_trend < 0 else "declining",
            "accuracy_slope": accuracy_trend,
            "performance_slope": performance_trend
        }
    
    async def _identify_optimizations(self, metrics: Dict) -> List[Dict]:
        """🎯 Identyfikacja możliwości optymalizacji"""
        
        optimizations = []
        
        # GPU utilization optimization
        gpu_util = metrics.get("resources", {}).get("gpu_utilization", 0)
        if gpu_util < 50:
            optimizations.append({
                "type": "gpu_optimization",
                "description": "GPU utilization is low - consider batch processing",
                "impact": "high",
                "effort": "medium"
            })
        
        # RTF factor optimization
        rtf = metrics.get("performance", {}).get("rtf_factor", 0)
        if rtf < self.thresholds["rtf_factor_min"]:
            optimizations.append({
                "type": "inference_optimization",
                "description": "RTF factor below 2024 standards - upgrade to latest NeMo optimizations",
                "impact": "very_high",
                "effort": "high"
            })
        
        return optimizations
    
    async def _predict_maintenance_needs(self, metrics: Dict) -> Dict:
        """🔮 Predykcja potrzeb maintenance"""
        
        maintenance_score = 0
        recommendations = []
        
        # Model performance degradation prediction
        if len(self.metrics_history) >= 10:
            recent_accuracy = [m.word_accuracy for m in self.metrics_history[-10:]]
            accuracy_variance = np.var(recent_accuracy)
            
            if accuracy_variance > 0.01:  # High variance indicates instability
                maintenance_score += 30
                recommendations.append("Model retraining recommended due to accuracy variance")
        
        # Resource usage prediction
        memory_usage = metrics.get("resources", {}).get("memory_usage", 0)
        if memory_usage > 85:
            maintenance_score += 20
            recommendations.append("Memory optimization needed")
        
        return {
            "maintenance_score": maintenance_score,
            "maintenance_needed": maintenance_score > 50,
            "recommendations": recommendations,
            "next_maintenance_window": "within_7_days" if maintenance_score > 70 else "within_30_days"
        }
    
    def _calculate_system_grade(self, metrics: Dict) -> PerformanceGrade:
        """🏆 Obliczenie oceny systemu"""
        
        scores = []
        
        # Accuracy score (40% weight)
        word_acc = metrics.get("accuracy", {}).get("word_accuracy", 0)
        accuracy_score = min(100, (word_acc / self.thresholds["word_accuracy_min"]) * 100)
        scores.append(accuracy_score * 0.4)
        
        # Performance score (30% weight)
        proc_time = metrics.get("performance", {}).get("processing_time", 999)
        performance_score = min(100, (self.thresholds["processing_time_max"] / max(proc_time, 1)) * 100)
        scores.append(performance_score * 0.3)
        
        # RTF factor score (20% weight)
        rtf = metrics.get("performance", {}).get("rtf_factor", 0)
        rtf_score = min(100, (rtf / self.thresholds["rtf_factor_min"]) * 100)
        scores.append(rtf_score * 0.2)
        
        # Resource efficiency score (10% weight)
        gpu_util = metrics.get("resources", {}).get("gpu_utilization", 0)
        resource_score = min(100, (gpu_util / self.thresholds["gpu_utilization_optimal"]) * 100)
        scores.append(resource_score * 0.1)
        
        total_score = sum(scores)
        
        if total_score >= 95:
            return PerformanceGrade.EXCELLENT
        elif total_score >= 90:
            return PerformanceGrade.VERY_GOOD
        elif total_score >= 85:
            return PerformanceGrade.GOOD
        elif total_score >= 80:
            return PerformanceGrade.ACCEPTABLE
        elif total_score >= 70:
            return PerformanceGrade.NEEDS_WORK
        else:
            return PerformanceGrade.POOR

    async def _generate_smart_recommendations(self, services: Dict, metrics: Dict, ai_insights: Dict) -> List[Dict]:
        """💡 Generowanie inteligentnych rekomendacji"""

        recommendations = []

        # Service-specific recommendations
        for service_name, service_data in services.items():
            if service_data.get("status") != SystemStatus.HEALTHY.value:
                recommendations.append({
                    "type": "service_health",
                    "service": service_name,
                    "priority": "high",
                    "action": f"Investigate and restore {service_name} service",
                    "estimated_impact": "system_stability"
                })

        # Performance recommendations
        if ai_insights.get("anomaly_detection", {}).get("anomalies_detected"):
            for anomaly in ai_insights["anomaly_detection"]["anomalies"]:
                recommendations.append({
                    "type": "performance_optimization",
                    "priority": anomaly["severity"],
                    "action": f"Address {anomaly['type']} in {anomaly['metric']}",
                    "current_value": anomaly["value"],
                    "target_value": anomaly["threshold"],
                    "estimated_impact": "performance_improvement"
                })

        # Optimization recommendations
        for optimization in ai_insights.get("optimization_opportunities", []):
            recommendations.append({
                "type": "optimization",
                "priority": "medium",
                "action": optimization["description"],
                "impact": optimization["impact"],
                "effort": optimization["effort"]
            })

        # Maintenance recommendations
        maintenance = ai_insights.get("predictive_maintenance", {})
        if maintenance.get("maintenance_needed"):
            for rec in maintenance.get("recommendations", []):
                recommendations.append({
                    "type": "maintenance",
                    "priority": "medium",
                    "action": rec,
                    "timeline": maintenance.get("next_maintenance_window"),
                    "estimated_impact": "system_reliability"
                })

        return recommendations

    def _determine_overall_status(self, services: Dict, metrics: Dict) -> SystemStatus:
        """🎯 Określenie ogólnego statusu systemu"""

        # Check critical services
        critical_services = ["nvidia_stt", "orchestrator"]
        for service in critical_services:
            if services.get(service, {}).get("status") == SystemStatus.OFFLINE.value:
                return SystemStatus.CRITICAL

        # Check performance thresholds
        word_acc = metrics.get("accuracy", {}).get("word_accuracy", 0)
        if word_acc < 80:  # Below acceptable threshold
            return SystemStatus.CRITICAL

        proc_time = metrics.get("performance", {}).get("processing_time", 0)
        if proc_time > 60:  # Double the acceptable threshold
            return SystemStatus.DEGRADED

        # Check if any services are degraded
        degraded_count = sum(1 for s in services.values()
                           if s.get("status") == SystemStatus.DEGRADED.value)
        if degraded_count > 1:
            return SystemStatus.DEGRADED

        return SystemStatus.HEALTHY

    async def run_continuous_monitoring(self, interval_seconds: int = 300) -> None:
        """🔄 Ciągły monitoring systemu"""

        logger.info(f"🚀 Starting continuous monitoring (interval: {interval_seconds}s)")

        while True:
            try:
                # Perform health check
                health_report = await self.perform_ultimate_health_check()

                # Store metrics
                if health_report.get("performance_metrics"):
                    metrics = self._convert_to_system_metrics(health_report["performance_metrics"])
                    self.metrics_history.append(metrics)

                    # Keep only last 24 hours of data
                    cutoff_time = datetime.now() - timedelta(hours=24)
                    self.metrics_history = [m for m in self.metrics_history
                                          if m.timestamp > cutoff_time]

                # Generate alerts if needed
                await self._process_alerts(health_report)

                # Save health report
                await self._save_health_report(health_report)

                logger.info(f"✅ Health check completed - Status: {health_report['overall_status']}")

            except Exception as e:
                logger.error(f"❌ Monitoring cycle failed: {e}")

            await asyncio.sleep(interval_seconds)

    def _convert_to_system_metrics(self, metrics_dict: Dict) -> SystemMetrics:
        """📊 Konwersja dict do SystemMetrics"""

        return SystemMetrics(
            timestamp=datetime.now(),
            word_accuracy=metrics_dict.get("accuracy", {}).get("word_accuracy", 0),
            hvac_accuracy=metrics_dict.get("accuracy", {}).get("hvac_accuracy", 0),
            processing_time=metrics_dict.get("performance", {}).get("processing_time", 0),
            confidence_score=metrics_dict.get("accuracy", {}).get("confidence", 0),
            rtf_factor=metrics_dict.get("performance", {}).get("rtf_factor", 0),
            gpu_utilization=metrics_dict.get("resources", {}).get("gpu_utilization", 0),
            memory_usage=metrics_dict.get("resources", {}).get("memory_usage", 0),
            error_rate=0.0,  # TODO: Calculate from actual errors
            throughput=metrics_dict.get("performance", {}).get("throughput", 0)
        )

    async def _process_alerts(self, health_report: Dict) -> None:
        """🚨 Przetwarzanie alertów"""

        alerts = []

        # Critical status alert
        if health_report["overall_status"] == SystemStatus.CRITICAL.value:
            alerts.append({
                "level": "critical",
                "message": "System is in critical state",
                "timestamp": datetime.now().isoformat(),
                "details": health_report
            })

        # Performance degradation alerts
        if health_report.get("grade") in [PerformanceGrade.NEEDS_WORK.value, PerformanceGrade.POOR.value]:
            alerts.append({
                "level": "warning",
                "message": f"System performance grade: {health_report['grade']}",
                "timestamp": datetime.now().isoformat(),
                "recommendations": health_report.get("recommendations", [])
            })

        # Store alerts
        self.alerts_history.extend(alerts)

        # Keep only last 7 days of alerts
        cutoff_time = datetime.now() - timedelta(days=7)
        self.alerts_history = [a for a in self.alerts_history
                             if datetime.fromisoformat(a["timestamp"]) > cutoff_time]

        # Send notifications (TODO: implement notification system)
        for alert in alerts:
            logger.warning(f"🚨 ALERT [{alert['level']}]: {alert['message']}")

    async def _save_health_report(self, health_report: Dict) -> None:
        """💾 Zapisanie raportu zdrowia"""

        os.makedirs("./monitoring_reports", exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"./monitoring_reports/health_report_{timestamp}.json"

        async with aiofiles.open(filename, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(health_report, indent=2, ensure_ascii=False))

    async def generate_ultimate_dashboard_data(self) -> Dict:
        """📊 Generowanie danych dla ultimate dashboard"""

        # Get latest health report
        health_report = await self.perform_ultimate_health_check()

        # Prepare dashboard data
        dashboard_data = {
            "system_overview": {
                "status": health_report["overall_status"],
                "grade": health_report["grade"],
                "last_updated": datetime.now().isoformat(),
                "uptime": "99.9%",  # TODO: Calculate actual uptime
                "total_requests": len(self.metrics_history)
            },
            "real_time_metrics": health_report["performance_metrics"],
            "service_health": health_report["services"],
            "ai_insights": health_report["ai_insights"],
            "recommendations": health_report["recommendations"],
            "historical_trends": self._prepare_historical_trends(),
            "alerts": self.alerts_history[-10:],  # Last 10 alerts
            "optimization_opportunities": health_report["ai_insights"].get("optimization_opportunities", [])
        }

        return dashboard_data

    def _prepare_historical_trends(self) -> Dict:
        """📈 Przygotowanie danych trendów historycznych"""

        if len(self.metrics_history) < 2:
            return {"status": "insufficient_data"}

        # Last 24 hours of data
        recent_metrics = self.metrics_history[-24:] if len(self.metrics_history) >= 24 else self.metrics_history

        trends = {
            "timestamps": [m.timestamp.isoformat() for m in recent_metrics],
            "word_accuracy": [m.word_accuracy for m in recent_metrics],
            "processing_time": [m.processing_time for m in recent_metrics],
            "rtf_factor": [m.rtf_factor for m in recent_metrics],
            "gpu_utilization": [m.gpu_utilization for m in recent_metrics],
            "confidence_scores": [m.confidence_score for m in recent_metrics]
        }

        return trends

# 🚀 Main execution
async def main():
    """🎯 Główna funkcja Ultimate System Integrator"""

    print("🚀 ULTIMATE SYSTEM INTEGRATOR - 2024 EDITION")
    print("=" * 60)
    print("🎯 Next-level integration with AI-powered monitoring")
    print("📊 Real-time performance analysis")
    print("🧠 Predictive maintenance capabilities")
    print("⚡ 10x performance optimization support")
    print()

    integrator = UltimateSystemIntegrator()

    # Perform ultimate health check
    health_report = await integrator.perform_ultimate_health_check()

    print("🏥 ULTIMATE HEALTH CHECK RESULTS")
    print("=" * 40)
    print(f"🎯 Overall Status: {health_report['overall_status'].upper()}")
    print(f"🏆 System Grade: {health_report['grade']}")
    print(f"📊 Services Checked: {len(health_report['services'])}")
    print(f"💡 Recommendations: {len(health_report['recommendations'])}")

    # Show key metrics
    if health_report.get("performance_metrics"):
        metrics = health_report["performance_metrics"]
        print("\n📈 KEY PERFORMANCE METRICS")
        print("-" * 30)

        if "accuracy" in metrics:
            acc = metrics["accuracy"]
            print(f"📝 Word Accuracy: {acc.get('word_accuracy', 0):.1f}%")
            print(f"🔧 HVAC Accuracy: {acc.get('hvac_accuracy', 0):.1f}%")

        if "performance" in metrics:
            perf = metrics["performance"]
            print(f"⚡ Processing Time: {perf.get('processing_time', 0):.2f}s")
            print(f"🚀 RTF Factor: {perf.get('rtf_factor', 0):.0f}")

    # Show top recommendations
    if health_report.get("recommendations"):
        print("\n💡 TOP RECOMMENDATIONS")
        print("-" * 25)
        for i, rec in enumerate(health_report["recommendations"][:3], 1):
            print(f"{i}. {rec.get('action', 'No action specified')}")

    print("\n🎆 ULTIMATE INTEGRATION READY!")
    print("📊 Dashboard data available")
    print("🔄 Continuous monitoring ready")
    print("🧠 AI insights operational")

if __name__ == "__main__":
    asyncio.run(main())
