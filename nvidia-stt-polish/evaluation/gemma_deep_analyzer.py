#!/usr/bin/env python3
"""
🧠 Gemma Deep Analysis Engine
Thoughtful reflection and insights for NVIDIA STT Polish HVAC system
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional
from datetime import datetime
import aiohttp
from pydantic import BaseModel

# 🔧 Konfiguracja logowania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepAnalysisResult(BaseModel):
    """🧠 Wynik głębokiej analizy Gemmy"""
    analysis_type: str
    timestamp: datetime
    input_data: Dict
    gemma_insights: str
    recommendations: List[str]
    confidence_level: float
    processing_time: float

class GemmaDeepAnalyzer:
    """🧠 Silnik głębokiej analizy z Gemmą 3-4B"""
    
    def __init__(self, lm_studio_url: str = "http://192.168.0.179:1234"):
        self.lm_studio_url = lm_studio_url
        self.model_name = "gemma-3-4b"
        self.analysis_results: List[DeepAnalysisResult] = []
        
        # 🎯 Prompts for different analysis types
        self.analysis_prompts = {
            "stt_quality_reflection": """
Jako ekspert AI w dziedzinie rozpoznawania mowy, przeanalizuj głęboko wyniki ewaluacji systemu NVIDIA STT dla polskiego języka w kontekście HVAC.

Dane ewaluacji:
{evaluation_data}

Proszę o thoughtful reflection na następujące aspekty:

1. 🎯 JAKOŚĆ TRANSKRYPCJI
   - Analiza accuracy metrics (word, character, HVAC terms)
   - Identyfikacja wzorców błędów
   - Ocena performance dla terminologii HVAC

2. 🔍 OBSZARY DO POPRAWY
   - Konkretne słabości systemu
   - Sugestie optymalizacji modelu
   - Propozycje wzbogacenia słownictwa

3. 🚀 STRATEGICZNE REKOMENDACJE
   - Kroki do osiągnięcia 95%+ accuracy
   - Optymalizacja dla polskiego języka
   - Integracja z business processes

4. 🎨 INNOWACYJNE PODEJŚCIA
   - Wykorzystanie context-aware processing
   - Adaptive learning mechanisms
   - Multi-modal integration possibilities

Odpowiedz w sposób przemyślany, szczegółowy i praktyczny. Skup się na actionable insights.
""",
            
            "system_architecture_analysis": """
Jako senior architect systemów AI, przeanalizuj architekturę NVIDIA STT Polish HVAC pipeline.

Architektura systemu:
{system_data}

Proszę o deep analysis następujących aspektów:

1. 🏗️ ARCHITECTURAL EXCELLENCE
   - Ocena current design patterns
   - Scalability i performance considerations
   - Integration points analysis

2. 🔄 WORKFLOW OPTIMIZATION
   - Email → M4A → STT → Analysis pipeline
   - Bottlenecks identification
   - Throughput optimization opportunities

3. 🛡️ RELIABILITY & RESILIENCE
   - Fallback mechanisms effectiveness
   - Error handling strategies
   - Monitoring and alerting gaps

4. 🚀 FUTURE-PROOFING
   - Technology evolution readiness
   - Extensibility for new features
   - Cloud-native transformation path

Przedstaw thoughtful recommendations z konkretnym action plan.
""",
            
            "business_impact_assessment": """
Jako business analyst z expertise w AI transformation, oceń business impact NVIDIA STT systemu dla HVAC CRM.

Business context:
{business_data}

Przeanalizuj głęboko:

1. 💼 BUSINESS VALUE CREATION
   - ROI calculation i value drivers
   - Operational efficiency gains
   - Customer experience improvements

2. 📊 PERFORMANCE METRICS
   - KPI achievement analysis
   - Benchmark comparison
   - Success criteria evaluation

3. 🎯 STRATEGIC ALIGNMENT
   - Alignment z business objectives
   - Competitive advantage creation
   - Market positioning impact

4. 🔮 FUTURE OPPORTUNITIES
   - Expansion possibilities
   - New revenue streams
   - Innovation potential

Dostarczaj actionable business insights z quantified benefits.
""",
            
            "continuous_improvement_strategy": """
Jako expert w continuous improvement i AI optimization, opracuj strategię ciągłego doskonalenia NVIDIA STT systemu.

Current system status:
{improvement_data}

Thoughtful analysis:

1. 🔄 LEARNING MECHANISMS
   - Feedback loops implementation
   - Model adaptation strategies
   - Performance monitoring systems

2. 📈 OPTIMIZATION ROADMAP
   - Short-term improvements (1-3 months)
   - Medium-term enhancements (3-12 months)
   - Long-term vision (1-3 years)

3. 🧪 EXPERIMENTATION FRAMEWORK
   - A/B testing strategies
   - Model versioning approaches
   - Risk mitigation plans

4. 🎯 SUCCESS MEASUREMENT
   - Metrics definition
   - Baseline establishment
   - Progress tracking methods

Przedstaw comprehensive improvement strategy z timeline i milestones.
"""
        }
    
    async def analyze_stt_quality(self, evaluation_report: Dict) -> DeepAnalysisResult:
        """🎯 Głęboka analiza jakości STT"""
        
        prompt = self.analysis_prompts["stt_quality_reflection"].format(
            evaluation_data=json.dumps(evaluation_report, indent=2)
        )
        
        return await self._perform_deep_analysis(
            "stt_quality_reflection",
            {"evaluation_report": evaluation_report},
            prompt
        )
    
    async def analyze_system_architecture(self, system_info: Dict) -> DeepAnalysisResult:
        """🏗️ Analiza architektury systemu"""
        
        prompt = self.analysis_prompts["system_architecture_analysis"].format(
            system_data=json.dumps(system_info, indent=2)
        )
        
        return await self._perform_deep_analysis(
            "system_architecture_analysis",
            {"system_info": system_info},
            prompt
        )
    
    async def analyze_business_impact(self, business_metrics: Dict) -> DeepAnalysisResult:
        """💼 Analiza business impact"""
        
        prompt = self.analysis_prompts["business_impact_assessment"].format(
            business_data=json.dumps(business_metrics, indent=2)
        )
        
        return await self._perform_deep_analysis(
            "business_impact_assessment",
            {"business_metrics": business_metrics},
            prompt
        )
    
    async def create_improvement_strategy(self, current_status: Dict) -> DeepAnalysisResult:
        """🔄 Strategia ciągłego doskonalenia"""
        
        prompt = self.analysis_prompts["continuous_improvement_strategy"].format(
            improvement_data=json.dumps(current_status, indent=2)
        )
        
        return await self._perform_deep_analysis(
            "continuous_improvement_strategy",
            {"current_status": current_status},
            prompt
        )
    
    async def _perform_deep_analysis(self, analysis_type: str, input_data: Dict, prompt: str) -> DeepAnalysisResult:
        """🧠 Wykonanie głębokiej analizy przez Gemmę"""
        
        start_time = time.time()
        
        try:
            logger.info(f"🧠 Starting deep analysis: {analysis_type}")
            
            # 🤖 Wywołanie Gemmy 3-4B
            gemma_response = await self._call_gemma(prompt)
            
            # 📝 Parsowanie recommendations
            recommendations = self._extract_recommendations(gemma_response)
            
            # 📊 Ocena confidence
            confidence = self._assess_confidence(gemma_response)
            
            processing_time = time.time() - start_time
            
            result = DeepAnalysisResult(
                analysis_type=analysis_type,
                timestamp=datetime.now(),
                input_data=input_data,
                gemma_insights=gemma_response,
                recommendations=recommendations,
                confidence_level=confidence,
                processing_time=processing_time
            )
            
            self.analysis_results.append(result)
            
            logger.info(f"✅ Deep analysis completed: {analysis_type} ({processing_time:.2f}s)")
            return result
            
        except Exception as e:
            logger.error(f"❌ Deep analysis failed for {analysis_type}: {e}")
            raise
    
    async def _call_gemma(self, prompt: str) -> str:
        """🤖 Wywołanie Gemmy 3-4B przez LM Studio"""
        
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "system",
                    "content": "Jesteś ekspertem AI w dziedzinie rozpoznawania mowy i systemów HVAC. Dostarczasz thoughtful, detailed analysis z actionable recommendations. Odpowiadasz po polsku w sposób profesjonalny i praktyczny."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 2048,
            "stream": False
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    raise Exception(f"Gemma API call failed: {response.status}")
    
    def _extract_recommendations(self, gemma_response: str) -> List[str]:
        """📝 Wyodrębnienie rekomendacji z odpowiedzi Gemmy"""
        
        recommendations = []
        lines = gemma_response.split('\n')
        
        for line in lines:
            line = line.strip()
            # Szukanie linii z rekomendacjami (zaczynających się od numerów, -, •, etc.)
            if (line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•', '*')) and 
                len(line) > 10):
                recommendations.append(line)
        
        return recommendations[:10]  # Maksymalnie 10 rekomendacji
    
    def _assess_confidence(self, gemma_response: str) -> float:
        """📊 Ocena confidence na podstawie odpowiedzi"""
        
        # Prosta heurystyka confidence na podstawie długości i struktury odpowiedzi
        response_length = len(gemma_response)
        
        if response_length < 500:
            return 0.6
        elif response_length < 1000:
            return 0.75
        elif response_length < 2000:
            return 0.85
        else:
            return 0.9
    
    async def generate_comprehensive_insights_report(self) -> Dict:
        """📊 Generowanie kompletnego raportu insights"""
        
        if not self.analysis_results:
            return {"error": "No analysis results available"}
        
        report = {
            "comprehensive_insights": {
                "timestamp": datetime.now().isoformat(),
                "total_analyses": len(self.analysis_results),
                "average_confidence": sum(r.confidence_level for r in self.analysis_results) / len(self.analysis_results),
                "total_processing_time": sum(r.processing_time for r in self.analysis_results)
            },
            "analysis_summary": {},
            "consolidated_recommendations": [],
            "key_insights": []
        }
        
        # 📝 Podsumowanie każdej analizy
        for result in self.analysis_results:
            report["analysis_summary"][result.analysis_type] = {
                "confidence": result.confidence_level,
                "recommendations_count": len(result.recommendations),
                "processing_time": result.processing_time,
                "key_points": result.recommendations[:3]  # Top 3 recommendations
            }
        
        # 🎯 Konsolidacja rekomendacji
        all_recommendations = []
        for result in self.analysis_results:
            all_recommendations.extend(result.recommendations)
        
        report["consolidated_recommendations"] = all_recommendations
        
        # 💾 Zapisanie raportu
        report_file = f"gemma_insights_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("./evaluation_results", exist_ok=True)
        
        with open(f"./evaluation_results/{report_file}", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"🧠 Gemma insights report saved: {report_file}")
        return report
    
    async def thoughtful_reflection_session(self, evaluation_data: Dict, system_info: Dict) -> Dict:
        """🧠 Sesja thoughtful reflection - spokojny czas dla przemyślenia"""
        
        logger.info("🧠 Starting thoughtful reflection session with Gemma...")
        logger.info("⏰ Taking time for deep thinking and analysis...")
        
        # 🎯 Sekwencja analiz z przerwami na "przemyślenie"
        analyses = []
        
        # 1. Analiza jakości STT
        logger.info("🎤 Phase 1: STT Quality Deep Analysis...")
        await asyncio.sleep(2)  # Spokojny czas na przemyślenie
        stt_analysis = await self.analyze_stt_quality(evaluation_data)
        analyses.append(stt_analysis)
        
        # 2. Analiza architektury
        logger.info("🏗️ Phase 2: System Architecture Reflection...")
        await asyncio.sleep(3)  # Więcej czasu na architekturę
        arch_analysis = await self.analyze_system_architecture(system_info)
        analyses.append(arch_analysis)
        
        # 3. Business impact
        logger.info("💼 Phase 3: Business Impact Assessment...")
        await asyncio.sleep(2)
        business_analysis = await self.analyze_business_impact({
            "current_metrics": evaluation_data,
            "system_status": system_info
        })
        analyses.append(business_analysis)
        
        # 4. Strategia doskonalenia
        logger.info("🔄 Phase 4: Continuous Improvement Strategy...")
        await asyncio.sleep(3)  # Najważniejsza analiza - więcej czasu
        improvement_analysis = await self.create_improvement_strategy({
            "evaluation_results": evaluation_data,
            "system_architecture": system_info,
            "previous_analyses": [a.gemma_insights for a in analyses]
        })
        analyses.append(improvement_analysis)
        
        # 📊 Generowanie comprehensive report
        logger.info("📊 Generating comprehensive insights report...")
        comprehensive_report = await self.generate_comprehensive_insights_report()
        
        logger.info("✅ Thoughtful reflection session completed!")
        return comprehensive_report

# 🚀 Main execution
async def main():
    """🧠 Główna funkcja deep analysis"""
    
    analyzer = GemmaDeepAnalyzer()
    
    # 📊 Mock data for testing
    mock_evaluation = {
        "accuracy_metrics": {
            "word_accuracy": {"mean": 87.5},
            "hvac_terms_accuracy": {"mean": 92.3}
        },
        "performance_metrics": {
            "average_processing_time": 2.1
        }
    }
    
    mock_system_info = {
        "architecture": "NVIDIA NeMo + Fallback",
        "components": ["STT", "Audio Converter", "Email Processor", "Gemma Integration"],
        "deployment": "Docker Compose Production"
    }
    
    # 🧠 Thoughtful reflection session
    insights = await analyzer.thoughtful_reflection_session(mock_evaluation, mock_system_info)
    
    print("\n🧠 GEMMA DEEP ANALYSIS COMPLETED!")
    print("=" * 50)
    print(f"📊 Total analyses: {insights['comprehensive_insights']['total_analyses']}")
    print(f"🎯 Average confidence: {insights['comprehensive_insights']['average_confidence']:.2f}")
    print(f"📝 Total recommendations: {len(insights['consolidated_recommendations'])}")

if __name__ == "__main__":
    asyncio.run(main())
