# 🧪 Evaluation Tools Requirements
# Dependencies for comprehensive NVIDIA STT evaluation and analysis

# 🚀 Core async and HTTP
aiohttp==3.9.1
aiofiles==23.2.1
asyncio-mqtt==0.13.0

# 📊 Data analysis and visualization
pandas==2.0.3
numpy==1.24.3
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.17.0

# 📈 Dashboard and web interface
streamlit==1.28.1
streamlit-plotly-events==0.0.6

# 🔧 Data validation and models
pydantic==2.5.0

# 📊 Statistical analysis
scipy==1.11.4
scikit-learn==1.3.2

# 🎨 Additional visualization
bokeh==3.3.0
altair==5.1.2

# 📝 Report generation
reportlab==4.0.7
jinja2==3.1.2

# 🔍 Text analysis and NLP
difflib2==0.1.2
textdistance==4.6.0

# 🧪 Testing and validation
pytest==7.4.3
pytest-asyncio==0.21.1

# 🔧 Utilities
python-dotenv==1.0.0
click==8.1.7
tqdm==4.66.1
colorama==0.4.6

# 📊 Time series analysis
statsmodels==0.14.0

# 🎯 Performance monitoring
psutil==5.9.6
memory-profiler==0.61.0
