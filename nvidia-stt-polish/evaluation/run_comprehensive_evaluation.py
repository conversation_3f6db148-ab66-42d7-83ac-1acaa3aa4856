#!/usr/bin/env python3
"""
🎯 Comprehensive Evaluation Orchestrator
Master script for running complete NVIDIA STT evaluation and analysis
"""

import os
import asyncio
import logging
import json
import time
from datetime import datetime
from pathlib import Path

from stt_quality_evaluator import HVACSTTEvaluator
from gemma_deep_analyzer import GemmaDeepAnalyzer

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./evaluation_results/evaluation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveEvaluationOrchestrator:
    """🎯 Orchestrator kompletnej ewaluacji systemu"""
    
    def __init__(self):
        self.stt_evaluator = HVACSTTEvaluator()
        self.gemma_analyzer = GemmaDeepAnalyzer()
        
        # 📁 Przygotowanie katalogów
        self.setup_directories()
        
    def setup_directories(self):
        """📁 Przygotowanie struktury katalogów"""
        
        directories = [
            "./evaluation_results",
            "./test_audio",
            "./evaluation_charts",
            "./gemma_insights"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"📁 Directory ready: {directory}")
    
    async def run_complete_evaluation_cycle(self) -> Dict:
        """🔄 Pełny cykl ewaluacji i analizy"""
        
        logger.info("🚀 STARTING COMPREHENSIVE EVALUATION CYCLE")
        logger.info("=" * 60)
        
        start_time = time.time()
        results = {
            "cycle_start": datetime.now().isoformat(),
            "phases": {},
            "summary": {}
        }
        
        try:
            # 🎯 PHASE 1: STT Quality Evaluation
            logger.info("📊 PHASE 1: STT Quality Evaluation")
            logger.info("-" * 40)
            
            phase1_start = time.time()
            evaluation_report = await self.stt_evaluator.run_comprehensive_evaluation()
            
            if evaluation_report:
                # Generowanie wizualizacji
                self.stt_evaluator.create_evaluation_visualizations(evaluation_report)
                
                results["phases"]["stt_evaluation"] = {
                    "status": "completed",
                    "duration": time.time() - phase1_start,
                    "results": evaluation_report
                }
                logger.info(f"✅ Phase 1 completed in {time.time() - phase1_start:.2f}s")
            else:
                results["phases"]["stt_evaluation"] = {
                    "status": "failed",
                    "duration": time.time() - phase1_start,
                    "error": "No evaluation results generated"
                }
                logger.error("❌ Phase 1 failed - no evaluation results")
            
            # 🧠 PHASE 2: Gemma Deep Analysis
            logger.info("\n🧠 PHASE 2: Gemma Deep Analysis")
            logger.info("-" * 40)
            
            phase2_start = time.time()
            
            # Przygotowanie danych systemowych
            system_info = await self.gather_system_information()
            
            # Thoughtful reflection session
            insights_report = await self.gemma_analyzer.thoughtful_reflection_session(
                evaluation_report if evaluation_report else {},
                system_info
            )
            
            results["phases"]["gemma_analysis"] = {
                "status": "completed",
                "duration": time.time() - phase2_start,
                "results": insights_report
            }
            logger.info(f"✅ Phase 2 completed in {time.time() - phase2_start:.2f}s")
            
            # 📊 PHASE 3: Comprehensive Reporting
            logger.info("\n📊 PHASE 3: Comprehensive Reporting")
            logger.info("-" * 40)
            
            phase3_start = time.time()
            comprehensive_report = await self.generate_master_report(
                evaluation_report, insights_report, system_info
            )
            
            results["phases"]["reporting"] = {
                "status": "completed",
                "duration": time.time() - phase3_start,
                "results": comprehensive_report
            }
            logger.info(f"✅ Phase 3 completed in {time.time() - phase3_start:.2f}s")
            
            # 🎯 SUMMARY
            total_duration = time.time() - start_time
            results["cycle_end"] = datetime.now().isoformat()
            results["total_duration"] = total_duration
            
            results["summary"] = {
                "status": "success",
                "total_phases": 3,
                "completed_phases": len([p for p in results["phases"].values() if p["status"] == "completed"]),
                "total_duration": total_duration,
                "key_metrics": self.extract_key_metrics(evaluation_report, insights_report)
            }
            
            logger.info("\n🎆 COMPREHENSIVE EVALUATION CYCLE COMPLETED!")
            logger.info("=" * 60)
            logger.info(f"⏱️ Total duration: {total_duration:.2f}s")
            logger.info(f"✅ Completed phases: {results['summary']['completed_phases']}/3")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Evaluation cycle failed: {e}")
            results["summary"] = {
                "status": "failed",
                "error": str(e),
                "total_duration": time.time() - start_time
            }
            return results
    
    async def gather_system_information(self) -> Dict:
        """📊 Zbieranie informacji o systemie"""
        
        logger.info("📊 Gathering system information...")
        
        system_info = {
            "timestamp": datetime.now().isoformat(),
            "architecture": {
                "components": [
                    "NVIDIA NeMo STT Server",
                    "Audio Converter",
                    "Email Processor", 
                    "Gemma Integration",
                    "Transcription Orchestrator"
                ],
                "deployment": "Docker Compose Production",
                "fallback_strategy": "NVIDIA NeMo → ElevenLabs → Mock"
            },
            "configuration": {
                "stt_url": self.stt_evaluator.stt_url,
                "gemma_url": self.gemma_analyzer.lm_studio_url,
                "supported_languages": ["pl", "en"],
                "hvac_vocabulary_size": len(self.stt_evaluator.hvac_keywords)
            },
            "infrastructure": {
                "gpu_enabled": True,  # Assumption for production
                "docker_network": "python_mixer_hvac-network",
                "persistent_storage": ["logs", "models", "transcriptions", "analysis_results"]
            }
        }
        
        # Próba pobrania rzeczywistych informacji o systemie
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                # STT health check
                try:
                    async with session.get(f"{self.stt_evaluator.stt_url}/health", timeout=5) as response:
                        if response.status == 200:
                            stt_health = await response.json()
                            system_info["stt_service"] = stt_health
                except:
                    system_info["stt_service"] = {"status": "unavailable"}
                
                # Gemma health check
                try:
                    async with session.get(f"{self.gemma_analyzer.lm_studio_url}/v1/models", timeout=5) as response:
                        if response.status == 200:
                            gemma_models = await response.json()
                            system_info["gemma_service"] = gemma_models
                except:
                    system_info["gemma_service"] = {"status": "unavailable"}
        
        except Exception as e:
            logger.warning(f"⚠️ Could not gather live system info: {e}")
        
        return system_info
    
    def extract_key_metrics(self, evaluation_report: Dict, insights_report: Dict) -> Dict:
        """📊 Wyodrębnienie kluczowych metryk"""
        
        key_metrics = {}
        
        # Metryki z ewaluacji STT
        if evaluation_report and "accuracy_metrics" in evaluation_report:
            accuracy = evaluation_report["accuracy_metrics"]
            key_metrics["stt_performance"] = {
                "word_accuracy": accuracy.get("word_accuracy", {}).get("mean", 0),
                "hvac_accuracy": accuracy.get("hvac_terms_accuracy", {}).get("mean", 0),
                "processing_time": evaluation_report.get("performance_metrics", {}).get("average_processing_time", 0)
            }
        
        # Metryki z analizy Gemmy
        if insights_report and "comprehensive_insights" in insights_report:
            insights = insights_report["comprehensive_insights"]
            key_metrics["analysis_performance"] = {
                "total_analyses": insights.get("total_analyses", 0),
                "average_confidence": insights.get("average_confidence", 0),
                "recommendations_count": len(insights_report.get("consolidated_recommendations", []))
            }
        
        # Ocena ogólna
        if "stt_performance" in key_metrics:
            stt_perf = key_metrics["stt_performance"]
            word_acc = stt_perf["word_accuracy"]
            hvac_acc = stt_perf["hvac_accuracy"]
            proc_time = stt_perf["processing_time"]
            
            # Scoring system (0-100)
            accuracy_score = (word_acc + hvac_acc) / 2
            speed_score = max(0, 100 - (proc_time * 10))  # Penalty for slow processing
            
            key_metrics["overall_score"] = {
                "accuracy_score": accuracy_score,
                "speed_score": speed_score,
                "combined_score": (accuracy_score * 0.7 + speed_score * 0.3),
                "grade": self.calculate_grade((accuracy_score * 0.7 + speed_score * 0.3))
            }
        
        return key_metrics
    
    def calculate_grade(self, score: float) -> str:
        """🎯 Obliczenie oceny na podstawie score"""
        
        if score >= 95:
            return "A+"
        elif score >= 90:
            return "A"
        elif score >= 85:
            return "B+"
        elif score >= 80:
            return "B"
        elif score >= 75:
            return "C+"
        elif score >= 70:
            return "C"
        else:
            return "D"
    
    async def generate_master_report(self, evaluation_report: Dict, insights_report: Dict, system_info: Dict) -> Dict:
        """📊 Generowanie master raportu"""
        
        logger.info("📊 Generating master report...")
        
        master_report = {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "report_version": "1.0",
                "system": "NVIDIA STT Polish HVAC Pipeline"
            },
            "executive_summary": {},
            "detailed_evaluation": evaluation_report,
            "deep_insights": insights_report,
            "system_information": system_info,
            "recommendations": {},
            "next_steps": []
        }
        
        # Executive summary
        key_metrics = self.extract_key_metrics(evaluation_report, insights_report)
        master_report["executive_summary"] = {
            "overall_assessment": "System evaluation completed successfully",
            "key_metrics": key_metrics,
            "status": "Production Ready" if key_metrics.get("overall_score", {}).get("combined_score", 0) > 80 else "Needs Improvement"
        }
        
        # Consolidated recommendations
        if insights_report and "consolidated_recommendations" in insights_report:
            recommendations = insights_report["consolidated_recommendations"]
            master_report["recommendations"] = {
                "priority_1": recommendations[:3],
                "priority_2": recommendations[3:6],
                "priority_3": recommendations[6:10]
            }
        
        # Next steps
        master_report["next_steps"] = [
            "Deploy NVIDIA NeMo service to production",
            "Implement continuous monitoring",
            "Set up automated evaluation pipeline",
            "Configure alerting for quality degradation",
            "Plan regular model updates and retraining"
        ]
        
        # Zapisanie master raportu
        report_filename = f"master_evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = f"./evaluation_results/{report_filename}"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(master_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 Master report saved: {report_filename}")
        return master_report

# 🚀 Main execution
async def main():
    """🎯 Główna funkcja orchestratora"""
    
    print("🎯 NVIDIA STT COMPREHENSIVE EVALUATION ORCHESTRATOR")
    print("=" * 60)
    print("🎤 Evaluating STT quality")
    print("🧠 Running Gemma deep analysis") 
    print("📊 Generating comprehensive reports")
    print("⏰ Taking thoughtful time for reflection...")
    print()
    
    orchestrator = ComprehensiveEvaluationOrchestrator()
    
    # Uruchomienie pełnego cyklu ewaluacji
    results = await orchestrator.run_complete_evaluation_cycle()
    
    # Podsumowanie
    print("\n🎆 EVALUATION CYCLE SUMMARY")
    print("=" * 40)
    
    if results["summary"]["status"] == "success":
        print(f"✅ Status: {results['summary']['status'].upper()}")
        print(f"⏱️ Duration: {results['summary']['total_duration']:.2f}s")
        print(f"📊 Phases: {results['summary']['completed_phases']}/3")
        
        if "key_metrics" in results["summary"]:
            metrics = results["summary"]["key_metrics"]
            if "overall_score" in metrics:
                score = metrics["overall_score"]
                print(f"🎯 Overall Score: {score['combined_score']:.1f}/100 (Grade: {score['grade']})")
    else:
        print(f"❌ Status: {results['summary']['status'].upper()}")
        print(f"💥 Error: {results['summary'].get('error', 'Unknown error')}")
    
    print("\n📁 Results saved in: ./evaluation_results/")
    print("🚀 Ready for production deployment!")

if __name__ == "__main__":
    asyncio.run(main())
