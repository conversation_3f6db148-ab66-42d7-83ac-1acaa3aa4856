#!/bin/bash
# 🚀 Quick Start Evaluation Script
# One-command setup and execution of comprehensive NVIDIA STT evaluation

set -e

echo "🎯 NVIDIA STT EVALUATION - QUICK START"
echo "======================================"

# 🔧 Configuration
PYTHON_ENV="evaluation_env"
CURRENT_DIR=$(pwd)

echo ""
echo "📁 Setting up evaluation environment..."

# 🐍 Create Python virtual environment
if [ ! -d "$PYTHON_ENV" ]; then
    echo "🐍 Creating Python virtual environment..."
    python3 -m venv $PYTHON_ENV
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# 🔧 Activate environment
echo "🔧 Activating virtual environment..."
source $PYTHON_ENV/bin/activate

# 📦 Install dependencies
echo "📦 Installing evaluation dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

echo "✅ Dependencies installed"

# 📁 Create necessary directories
echo ""
echo "📁 Setting up directory structure..."
mkdir -p evaluation_results
mkdir -p test_audio
mkdir -p evaluation_charts
mkdir -p gemma_insights

echo "✅ Directory structure ready"

# 🎤 Check if test audio files exist
echo ""
echo "🎤 Checking test audio files..."

if [ ! -f "test_audio/test_hvac.wav" ]; then
    echo "🔊 Creating test audio file..."
    
    # Try to create a simple test audio file
    if command -v sox &> /dev/null; then
        sox -n -r 16000 -c 1 test_audio/test_hvac.wav synth 3 sine 440
        echo "✅ Test audio file created with sox"
    elif command -v ffmpeg &> /dev/null; then
        ffmpeg -f lavfi -i "sine=frequency=440:duration=3" -ar 16000 -ac 1 test_audio/test_hvac.wav -y
        echo "✅ Test audio file created with ffmpeg"
    else
        echo "⚠️ No audio generation tools available (sox/ffmpeg)"
        echo "📝 Please add your own test audio files to test_audio/ directory"
    fi
else
    echo "✅ Test audio files found"
fi

# 🏥 System health check
echo ""
echo "🏥 Performing system health check..."

# Check NVIDIA STT service
echo "🎤 Checking NVIDIA STT service..."
if curl -f http://localhost:8889/health > /dev/null 2>&1; then
    echo "✅ NVIDIA STT service is healthy"
else
    echo "⚠️ NVIDIA STT service not responding"
    echo "💡 Make sure to start the STT service first:"
    echo "   docker-compose -f docker-compose-production.yml up -d nvidia-nemo-stt"
fi

# Check Gemma service
echo "🧠 Checking Gemma service..."
if curl -f http://*************:1234/v1/models > /dev/null 2>&1; then
    echo "✅ Gemma service is accessible"
else
    echo "⚠️ Gemma service not accessible"
    echo "💡 Make sure LM Studio is running with Gemma model"
fi

# Check orchestrator
echo "🎯 Checking orchestrator service..."
if curl -f http://localhost:9000/health > /dev/null 2>&1; then
    echo "✅ Orchestrator service is healthy"
else
    echo "⚠️ Orchestrator service not responding"
    echo "💡 Make sure orchestrator is running"
fi

echo ""
echo "🎯 EVALUATION OPTIONS"
echo "===================="
echo ""
echo "Choose evaluation mode:"
echo "1. 🧪 Quick STT Quality Test"
echo "2. 🧠 Gemma Deep Analysis Only"
echo "3. 🎯 Comprehensive Evaluation (STT + Gemma)"
echo "4. 📊 Launch Quality Dashboard"
echo "5. 📈 View Previous Results"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🧪 Running Quick STT Quality Test..."
        python3 stt_quality_evaluator.py
        ;;
    2)
        echo ""
        echo "🧠 Running Gemma Deep Analysis..."
        python3 gemma_deep_analyzer.py
        ;;
    3)
        echo ""
        echo "🎯 Running Comprehensive Evaluation..."
        echo "⏰ This will take several minutes for thoughtful analysis..."
        python3 run_comprehensive_evaluation.py
        ;;
    4)
        echo ""
        echo "📊 Launching Quality Dashboard..."
        echo "🌐 Dashboard will open in your browser at http://localhost:8501"
        streamlit run quality_dashboard.py --server.port 8501
        ;;
    5)
        echo ""
        echo "📈 Previous Evaluation Results:"
        echo "=============================="
        
        if [ -d "evaluation_results" ] && [ "$(ls -A evaluation_results)" ]; then
            echo "📊 Available reports:"
            ls -la evaluation_results/ | grep -E "\.(json|png)$" | tail -10
            echo ""
            echo "📁 Results directory: evaluation_results/"
            echo "💡 Use 'cat evaluation_results/latest_report.json | jq .' to view JSON reports"
        else
            echo "📭 No previous results found"
            echo "💡 Run an evaluation first to generate results"
        fi
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎆 EVALUATION COMPLETED!"
echo "======================="
echo ""
echo "📁 Results saved in: evaluation_results/"
echo "📊 Charts saved in: evaluation_charts/"
echo "🧠 Insights saved in: gemma_insights/"
echo ""
echo "🚀 Next steps:"
echo "- Review evaluation results"
echo "- Implement recommended improvements"
echo "- Deploy to production when ready"
echo "- Set up continuous monitoring"
echo ""
echo "💡 To run dashboard: streamlit run quality_dashboard.py"
echo "💡 To re-run evaluation: python3 run_comprehensive_evaluation.py"

# 🔧 Deactivate virtual environment
deactivate
