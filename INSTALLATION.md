# Installation Guide

This guide provides step-by-step instructions for installing and setting up the HVAC-Remix project.

## Prerequisites
- Node.js (v14 or higher)
- npm (Node Package Manager)
- Git

## Steps to Install

1. **Clone the repository:**
   ```sh
   git clone https://github.com/your-repo/HVAC-Remix.git
   cd HVAC-Remix
   ```

2. **Install dependencies:**
   ```sh
   npm install
   ```

3. **Set up environment variables:**
   - Copy the `.env.example` file to `.env`.
   - Edit the `.env` file to configure your environment settings.

4. **Start the development server:**
   ```sh
   npm run dev
   ```

5. **Build the project:**
   ```sh
   npm run build
   ```

6. **Run the production server:**
   ```sh
   npm start
   ```

## Troubleshooting
- Ensure all dependencies are installed correctly.
- Check the console logs for any errors during installation.
- Verify that all environment variables are set properly.
