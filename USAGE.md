# Usage Guide

This guide provides instructions on how to use the HVAC-Remix project.

## Getting Started
1. **Clone the repository:**
   ```sh
   git clone https://github.com/your-repo/HVAC-Remix.git
   cd HVAC-Remix
   ```

2. **Install dependencies:**
   ```sh
   npm install
   ```

3. **Start the development server:**
   ```sh
   npm run dev
   ```

4. **Access the application:**
   - Open your web browser and navigate to [http://localhost:3000](http://localhost:3000).

## Features
- **Dashboard**: Overview of the system status and recent activities.
- **Leads**: Manage leads, including adding, editing, and deleting.
- **Campaigns**: Manage campaigns, including adding, editing, and deleting.
- **AI Insights**: View AI-driven insights and recommendations.
- **Financial Dashboard**: Monitor financial data and performance metrics.

## Common Tasks
- **Adding a New Lead:**
  1. Navigate to the Leads section.
  2. Click on "Add Lead".
  3. Fill in the required fields and save.

- **Creating a New Campaign:**
  1. Navigate to the Campaigns section.
  2. Click on "Create Campaign".
  3. Fill in the required fields and save.

- **Viewing AI Insights:**
  1. Navigate to the AI Insights section.
  2. Select the type of insight you want to view.
  3. Review the insights and recommendations.

## Troubleshooting
- Ensure all dependencies are installed correctly.
- Check the console logs for any errors during usage.
- Verify that all environment variables are set properly.
