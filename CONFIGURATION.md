# Configuration Guide

This guide provides instructions for configuring the HVAC-Remix project.

## Environment Variables
- **REACT_APP_API_URL**: URL of the backend API.
- **REACT_APP_ENV**: Environment type (development, production).
- **REACT_APP_DEBUG**: Enable debug mode (true/false).

## Backend Configuration
- **API_PORT**: Port number for the backend server.
- **DATABASE_URL**: URL of the PostgreSQL database.
- **AI_SERVICE_URL**: URL of the AI service.

## Example Configuration
```env
REACT_APP_API_URL=http://localhost:3000
REACT_APP_ENV=development
REACT_APP_DEBUG=true
API_PORT=3000
DATABASE_URL=postgresql://user:password@localhost:5432/hvac_db
AI_SERVICE_URL=http://localhost:8080
```

## Security Considerations
- Use secure environment variables for sensitive information.
- Regularly update dependencies to patch security vulnerabilities.
- Implement proper authentication and authorization mechanisms.
